import os
import sys
import time
import path_manager

class PasswordManager:
    """
    Lớ<PERSON> quản lý mật khẩu tập trung cho ứng dụng.
    Lưu trữ và cung cấp mật khẩu cho các thiết bị.
    """
    def __init__(self):
        # Dictionary lưu trữ mật khẩu theo serial của thiết bị
        self.device_passwords = {}
        # Dictionary lưu trữ mật khẩu theo chỉ số thiết bị
        self.index_passwords = {}
        # Mật khẩu mặc định nếu không tìm thấy
        self.default_password = ""
        # Đường dẫn đến file mail.txt
        self.mail_file_path = None
        # Danh sách email hợp lệ từ file mail.txt
        self.valid_emails = []

    def set_password(self, device_serial, password, device_index=None):
        """
        Thiết lập mật khẩu cho một thiết bị cụ thể.
        :param device_serial: Serial của thiết bị
        :param password: M<PERSON><PERSON> khẩu cần thiết lập
        :param device_index: Chỉ số thiết bị (tùy chọn)
        """
        # Lưu mật khẩu theo serial
        self.device_passwords[device_serial] = password
        print(f"✅ Đã thiết lập mật khẩu cho thiết bị {device_serial}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")

        # Nếu có chỉ số thiết bị, lưu mật khẩu theo chỉ số
        if device_index is not None:
            self.index_passwords[device_index] = password
            print(f"✅ Đã thiết lập mật khẩu cho thiết bị index {device_index}")

    def get_password(self, device_serial=None, device_index=None, table_widget=None):
        """
        Lấy mật khẩu cho một thiết bị.
        Ưu tiên theo thứ tự: tableWidget > serial > index > mặc định
        :param device_serial: Serial của thiết bị
        :param device_index: Chỉ số thiết bị
        :param table_widget: QTableWidget chứa thông tin mật khẩu (tùy chọn)
        :return: Mật khẩu của thiết bị
        """
        # Biến để lưu mật khẩu từ tableWidget
        table_password = None

        # Nếu có table_widget và device_index, ưu tiên lấy mật khẩu từ tableWidget
        if table_widget is not None and device_index is not None:
            try:
                # Lấy mật khẩu từ cột 2 (index 2)
                pass_item = table_widget.item(device_index, 2)
                if pass_item and pass_item.text().strip():
                    table_password = pass_item.text().strip()
                    print(f"✅ Đã lấy mật khẩu từ tableWidget cho thiết bị index {device_index}: {table_password[:2]}{'*' * (len(table_password) - 2) if len(table_password) > 2 else '*' * len(table_password)}")

                    # Cập nhật mật khẩu vào device_passwords và index_passwords
                    if device_serial:
                        self.device_passwords[device_serial] = table_password
                    self.index_passwords[device_index] = table_password

                    return table_password
            except Exception as e:
                print(f"⚠️ Lỗi khi lấy mật khẩu từ tableWidget: {str(e)}")

        # Ưu tiên lấy mật khẩu theo serial
        if device_serial and device_serial in self.device_passwords:
            password = self.device_passwords[device_serial]
            print(f"✅ Đã lấy mật khẩu cho thiết bị {device_serial}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
            return password

        # Nếu không có mật khẩu theo serial, thử lấy theo chỉ số
        if device_index is not None and device_index in self.index_passwords:
            password = self.index_passwords[device_index]
            print(f"✅ Đã lấy mật khẩu cho thiết bị index {device_index}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
            return password

        # Nếu không có mật khẩu theo serial và chỉ số, trả về mật khẩu mặc định
        if self.default_password:
            print(f"⚠️ Không tìm thấy mật khẩu cho thiết bị {device_serial or ''} (index {device_index or ''}), sử dụng mật khẩu mặc định")
            return self.default_password

        # Nếu không có mật khẩu mặc định, trả về chuỗi rỗng
        print(f"❌ Không tìm thấy mật khẩu cho thiết bị {device_serial or ''} (index {device_index or ''})")
        return ""

    def load_passwords_from_mail_file(self):
        """
        Đọc mật khẩu từ file mail.txt
        Format của file: email|password|authentication
        """
        try:
            # Tìm file mail.txt
            self._find_mail_file()

            if not self.mail_file_path or not os.path.exists(self.mail_file_path):
                print("❌ Không tìm thấy file mail.txt")
                return False

            # Đọc danh sách email từ file
            try:
                with open(self.mail_file_path, "r", encoding="utf-8") as file:
                    lines = file.readlines()
                print(f"✅ Đã đọc {len(lines)} dòng từ file {self.mail_file_path}")
            except Exception as e:
                print(f"❌ Lỗi khi đọc file {self.mail_file_path}: {str(e)}")
                # Thử đọc với encoding khác
                try:
                    with open(self.mail_file_path, "r") as file:
                        lines = file.readlines()
                    print(f"✅ Đã đọc {len(lines)} dòng từ file {self.mail_file_path} (không có encoding)")
                except Exception as e2:
                    print(f"❌ Lỗi khi đọc file {self.mail_file_path} (không có encoding): {str(e2)}")
                    return False

            # Lọc các dòng trống và dòng comment
            self.valid_emails = []
            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                self.valid_emails.append(line)

            print(f"✅ Đã lọc được {len(self.valid_emails)} dòng hợp lệ từ {len(lines)} dòng")

            # Kiểm tra xem có email nào không
            if not self.valid_emails:
                print(f"⚠️ Không có email nào trong file {self.mail_file_path}")
                print(f"⚠️ Vui lòng thêm email vào file với định dạng: email|password|authentication")
                return False

            # Lưu mật khẩu theo chỉ số
            for i, email_line in enumerate(self.valid_emails):
                email_info = email_line.split('|')
                if len(email_info) >= 2:
                    password = email_info[1].strip()
                    self.index_passwords[i] = password
                    print(f"✅ Đã lưu mật khẩu cho thiết bị index {i}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")

            return True
        except Exception as e:
            print(f"❌ Lỗi khi tải mật khẩu từ file mail.txt: {str(e)}")
            return False

    def _find_mail_file(self):
        """
        Tìm file mail.txt trong các đường dẫn có thể
        """
        # Danh sách các vị trí có thể chứa file mail.txt
        possible_paths = []

        # Kiểm tra xem có đang chạy từ file exe không
        is_frozen = getattr(sys, 'frozen', False)
        print(f"🔍 _find_mail_file: is_frozen = {is_frozen}")

        # Thêm đường dẫn đặc biệt cho file exe (ưu tiên cao nhất)
        if is_frozen:
            # Thêm đường dẫn tương đối với thư mục chứa file exe
            exe_dir = os.path.dirname(sys.executable)
            possible_paths.append(os.path.join(exe_dir, "data", "mail.txt"))
            possible_paths.append(os.path.join(exe_dir, "mail.txt"))

        # Thêm các đường dẫn thông thường
        base_path = os.path.abspath(".")
        possible_paths.extend([
            # Trong thư mục hiện tại
            os.path.join(base_path, "mail.txt"),
            # Trong thư mục data
            os.path.join(base_path, "data", "mail.txt"),
            # Trong thư mục cha
            os.path.join(os.path.dirname(base_path), "mail.txt"),
            # Trong thư mục data của thư mục cha
            os.path.join(os.path.dirname(base_path), "data", "mail.txt"),
        ])

        # Thử sử dụng path_manager
        try:
            # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
            mail_file_path = path_manager.get_txt_path("mail.txt")
            possible_paths.append(mail_file_path)
            print(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
        except Exception as e:
            print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")

        try:
            # Sử dụng get_data_path như phương án dự phòng
            mail_file_path = path_manager.get_data_path("mail.txt")
            possible_paths.append(mail_file_path)
            print(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")
        except Exception as e:
            print(f"⚠️ Lỗi khi sử dụng get_data_path: {str(e)}")

        # In thông tin debug
        print(f"🔍 Danh sách các đường dẫn có thể chứa mail.txt:")
        for idx, path in enumerate(possible_paths):
            print(f"  {idx+1}. {path}")

        # Tìm file mail.txt trong danh sách đường dẫn
        for path in possible_paths:
            if os.path.exists(path):
                self.mail_file_path = path
                print(f"✅ Đã tìm thấy file mail.txt tại: {self.mail_file_path}")
                return True

        print(f"⚠️ Không tìm thấy file mail.txt trong tất cả các đường dẫn")
        return False

    def load_passwords_from_table(self, table_widget):
        """
        Đọc mật khẩu từ bảng trong giao diện
        :param table_widget: QTableWidget chứa thông tin mật khẩu
        """
        try:
            row_count = table_widget.rowCount()
            print(f"🔍 Đọc mật khẩu từ bảng với {row_count} hàng")

            for row in range(row_count):
                # Lấy serial từ cột 1 (index 1)
                serial_item = table_widget.item(row, 1)
                if not serial_item:
                    continue

                serial = serial_item.text().strip()

                # Lấy mật khẩu từ cột 2 (index 2)
                pass_item = table_widget.item(row, 2)
                if not pass_item:
                    continue

                password = pass_item.text().strip()

                # Lưu mật khẩu theo serial
                if serial and password:
                    self.set_password(serial, password, row)

            return True
        except Exception as e:
            print(f"❌ Lỗi khi đọc mật khẩu từ bảng: {str(e)}")
            return False

# Tạo một instance toàn cục để sử dụng trong toàn bộ ứng dụng
password_manager = PasswordManager()
