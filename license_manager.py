"""
Module quản lý license key cho ứng dụng AutoK
Sử dụng Google Sheets để lưu trữ và quản lý license key
"""

import os
import sys
import gspread
import json
import uuid
import platform
import subprocess
import hashlib
import socket
import re
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import smtplib
from email.message import EmailMessage
import path_manager

# === Cấu hình Google Sheets ===
GOOGLE_SHEET_ID = "1tczccoz2w_aMJZaWb-syBJDbdMYQiGPfKKBpGcAhHW4"  # Thay bằng ID sheet thực tế
WORKSHEET_NAME = "Licenses"
CREDENTIALS_FILE = "google_creds.json"

# === Cấu hình Email Thông báo ===
EMAIL_FROM = "<EMAIL>"  # Thay bằng email thực tế
EMAIL_PASSWORD = ""  # Dùng App Password nếu là Gmail
EMAIL_TO = "<EMAIL>"     # Người nhận thông báo

# === Đường dẫn file license ===
def get_license_file_path():
    """Lấy đường dẫn đến file license.dat"""
    try:
        base_path = path_manager.get_base_path()
        print(f"🔍 Base path: {base_path}")

        # Ưu tiên 1: Tìm trong thư mục _internal/data
        if getattr(sys, 'frozen', False):
            # Nếu đang chạy từ file exe (đã đóng gói)
            internal_path = os.path.join(base_path, "_internal", "data", "license.dat")
            print(f"🔍 Đường dẫn file license.dat (_internal): {internal_path}")

            if os.path.exists(internal_path):
                print(f"✅ Đã tìm thấy file license.dat trong _internal")
                return internal_path

            # Nếu không tìm thấy trong _internal, sử dụng thư mục data bên cạnh file exe
            exe_dir = os.path.dirname(sys.executable)
            exe_path = os.path.join(exe_dir, "data", "license.dat")
            print(f"🔍 Đường dẫn file license.dat (exe): {exe_path}")

            # Đảm bảo thư mục tồn tại
            os.makedirs(os.path.dirname(exe_path), exist_ok=True)
            return exe_path
        else:
            # Nếu đang chạy từ script Python
            # Ưu tiên 1: Tìm trong thư mục _internal/data
            script_dir = os.path.dirname(os.path.abspath(__file__))
            internal_path = os.path.join(script_dir, "_internal", "data", "license.dat")
            print(f"🔍 Đường dẫn file license.dat (_internal): {internal_path}")

            if os.path.exists(internal_path):
                print(f"✅ Đã tìm thấy file license.dat trong _internal")
                return internal_path

            # Ưu tiên 2: Sử dụng thư mục data trong thư mục hiện tại
            current_dir = os.getcwd()
            current_path = os.path.join(current_dir, "data", "license.dat")
            print(f"🔍 Đường dẫn file license.dat (current_dir): {current_path}")

            # Đảm bảo thư mục tồn tại
            os.makedirs(os.path.dirname(current_path), exist_ok=True)
            return current_path

    except Exception as e:
        print(f"❌ Lỗi nghiêm trọng khi xác định đường dẫn file license: {str(e)}")

        # Fallback cuối cùng: sử dụng thư mục temp
        try:
            import tempfile
            temp_dir = os.path.join(tempfile.gettempdir(), "AutoK", "data")
            os.makedirs(temp_dir, exist_ok=True)
            temp_path = os.path.join(temp_dir, "license.dat")
            print(f"🔍 Sử dụng đường dẫn thư mục tạm: {temp_path}")
            return temp_path
        except:
            # Nếu tất cả đều thất bại, trả về đường dẫn cố định
            return os.path.join(os.getcwd(), "data", "license.dat")

# === Lấy UUID máy ===
def get_machine_uuid():
    """
    Lấy UUID duy nhất của máy tính
    Kết hợp nhiều thông tin phần cứng để tạo UUID ổn định
    """
    try:
        # Thử lấy UUID từ WMIC trên Windows
        if platform.system() == "Windows":
            try:
                uuid_output = subprocess.check_output("wmic csproduct get uuid").decode().split('\n')[1].strip()
                if uuid_output and uuid_output != "UUID" and len(uuid_output) > 5:
                    print(f"✅ Đã lấy UUID từ WMIC: {uuid_output}")
                    return uuid_output
            except Exception as e:
                print(f"⚠️ Không thể lấy UUID từ WMIC: {str(e)}")

        # Phương pháp thay thế nếu WMIC không hoạt động
        # Kết hợp nhiều thông tin phần cứng
        machine_info = []

        # Thêm hostname
        try:
            machine_info.append(socket.gethostname())
        except:
            machine_info.append("unknown-host")

        # Thêm MAC address của card mạng chính
        try:
            if platform.system() == "Windows":
                mac = subprocess.check_output("getmac").decode()
                mac_address = re.search(r"([0-9A-F]{2}[:-]){5}([0-9A-F]{2})", mac, re.I).group(0)
                machine_info.append(mac_address)
            else:
                # Phương pháp cho Linux/Mac
                mac = uuid.getnode()
                mac_address = ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))
                machine_info.append(mac_address)
        except:
            machine_info.append("unknown-mac")

        # Thêm thông tin CPU
        try:
            if platform.system() == "Windows":
                cpu_info = subprocess.check_output("wmic cpu get processorid").decode()
                processor_id = cpu_info.split('\n')[1].strip()
                machine_info.append(processor_id)
            else:
                # Phương pháp cho Linux/Mac
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if line.startswith('serial') or line.startswith('processor'):
                            machine_info.append(line.strip())
                            break
        except:
            machine_info.append("unknown-cpu")

        # Tạo hash từ thông tin máy
        combined_info = "-".join(machine_info)
        machine_hash = hashlib.md5(combined_info.encode()).hexdigest()
        print(f"✅ Đã tạo UUID từ thông tin phần cứng: {machine_hash}")
        return machine_hash
    except Exception as e:
        print(f"❌ Lỗi khi lấy UUID máy: {str(e)}")
        # Fallback: tạo UUID ngẫu nhiên nhưng cố định cho mỗi lần chạy
        fallback_uuid = str(uuid.uuid5(uuid.NAMESPACE_DNS, socket.gethostname()))
        print(f"⚠️ Sử dụng UUID dự phòng: {fallback_uuid}")
        return fallback_uuid

# === Gửi email thông báo kích hoạt mới ===
def send_activation_email(user, email, key, machine_uuid):
    """Gửi email thông báo khi có kích hoạt mới"""
    msg = EmailMessage()
    msg['Subject'] = f"[KÍCH HOẠT MỚI] Key {key} - Máy mới"
    msg['From'] = EMAIL_FROM
    msg['To'] = EMAIL_TO
    msg.set_content(f"""
Xin chào Admin,

Người dùng {user} ({email}) vừa kích hoạt key: {key} trên máy có UUID:

    {machine_uuid}

Vui lòng kiểm tra Google Sheet nếu cần kiểm soát thêm.

-- AutoK License Manager
""")
    try:
        with smtplib.SMTP_SSL('smtp.gmail.com', 465) as smtp:
            smtp.login(EMAIL_FROM, EMAIL_PASSWORD)
            smtp.send_message(msg)
            print(f"✅ Đã gửi email thông báo kích hoạt mới")
            return True
    except Exception as e:
        print(f"❌ Lỗi gửi email: {str(e)}")
        return False

# === Kết nối Google Sheet ===
def connect_sheet():
    """Kết nối đến Google Sheet chứa thông tin license"""
    try:
        # Ưu tiên 1: Nếu đang chạy từ file exe, tìm trong thư mục của file exe
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            possible_paths = [
                os.path.join(exe_dir, CREDENTIALS_FILE),
                os.path.join(exe_dir, "data", CREDENTIALS_FILE)
            ]
            print(f"🔍 Đang tìm file credentials trong thư mục exe: {exe_dir}")
        else:
            # Nếu không phải exe, sử dụng các đường dẫn khác
            possible_paths = []

        # Thêm các đường dẫn khác
        try:
            base_path = path_manager.get_base_path()
            possible_paths.extend([
                os.path.join(base_path, CREDENTIALS_FILE),
                os.path.join(base_path, "data", CREDENTIALS_FILE)
            ])
        except Exception as e:
            print(f"⚠️ Lỗi khi lấy base_path: {str(e)}")

        # Thêm đường dẫn tương đối với file hiện tại
        possible_paths.extend([
            os.path.join(os.path.dirname(os.path.abspath(__file__)), CREDENTIALS_FILE),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", CREDENTIALS_FILE)
        ])

        # Thêm đường dẫn trong AppData
        try:
            appdata = os.environ.get('APPDATA')
            if appdata:
                possible_paths.append(os.path.join(appdata, "AutoK", CREDENTIALS_FILE))
                possible_paths.append(os.path.join(appdata, "AutoK", "data", CREDENTIALS_FILE))
        except Exception as e:
            print(f"⚠️ Lỗi khi lấy đường dẫn AppData: {str(e)}")

        # Thêm đường dẫn hiện tại
        possible_paths.append(os.path.join(os.getcwd(), CREDENTIALS_FILE))
        possible_paths.append(os.path.join(os.getcwd(), "data", CREDENTIALS_FILE))

        # In ra tất cả các đường dẫn đang kiểm tra
        print(f"🔍 Danh sách đường dẫn có thể chứa file credentials:")
        for i, path in enumerate(possible_paths):
            print(f"  {i+1}. {path}")

        # Tìm file credentials trong danh sách đường dẫn
        creds_path = None
        for path in possible_paths:
            if os.path.exists(path):
                creds_path = path
                print(f"✅ Đã tìm thấy file credentials tại: {creds_path}")
                break

        if not creds_path:
            print(f"❌ Không tìm thấy file credentials {CREDENTIALS_FILE}")
            print(f"⚠️ Vui lòng đảm bảo file {CREDENTIALS_FILE} được đặt trong thư mục data hoặc thư mục gốc của ứng dụng")
            return None

        # Kiểm tra kích thước file
        file_size = os.path.getsize(creds_path)
        print(f"🔍 Kích thước file credentials: {file_size} bytes")
        if file_size < 100:  # File quá nhỏ, có thể bị hỏng
            print(f"⚠️ File credentials có kích thước bất thường: {file_size} bytes")
            print(f"⚠️ File có thể bị hỏng, vui lòng kiểm tra lại")

        # Kết nối đến Google Sheet
        try:
            scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
            creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
            client = gspread.authorize(creds)
            sheet = client.open_by_key(GOOGLE_SHEET_ID).worksheet(WORKSHEET_NAME)
            print(f"✅ Đã kết nối thành công đến Google Sheet")
            return sheet
        except Exception as e:
            print(f"❌ Lỗi khi xác thực với Google API: {str(e)}")
            print(f"⚠️ Chi tiết lỗi: {type(e).__name__}")
            if "invalid_grant" in str(e):
                print(f"⚠️ Lỗi xác thực Google API. File credentials có thể đã hết hạn hoặc không hợp lệ.")
            return None
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheet: {str(e)}")
        print(f"⚠️ Chi tiết lỗi: {type(e).__name__}")
        return None

# === Kiểm tra license ===
def check_license(key, machine_uuid=None):
    """
    Kiểm tra license key có hợp lệ không

    Args:
        key (str): License key cần kiểm tra
        machine_uuid (str, optional): UUID của máy. Nếu None, sẽ tự động lấy.

    Returns:
        tuple: (is_valid, message, features, user_info)
            - is_valid (bool): True nếu key hợp lệ
            - message (str): Thông báo kết quả
            - features (str): Các tính năng được phép sử dụng
            - user_info (dict): Thông tin user từ Google Sheets
    """
    if not machine_uuid:
        machine_uuid = get_machine_uuid()

    print(f"🔍 Kiểm tra license key: {key}")
    print(f"🔍 UUID máy: {machine_uuid}")

    # Kiểm tra offline trước (nếu đã lưu license)
    offline_result = check_license_offline(key, machine_uuid)
    if offline_result[0]:
        print(f"✅ Xác thực license offline thành công")
        # Đảm bảo offline_result có đủ 4 phần tử
        if len(offline_result) == 3:
            return offline_result + ({},)  # Thêm user_info rỗng
        return offline_result

    # Nếu offline không thành công, kiểm tra online
    sheet = connect_sheet()
    if not sheet:
        print(f"❌ Không thể kết nối đến Google Sheet, chỉ kiểm tra offline")

        # Hiển thị thông tin chi tiết về lỗi
        print(f"🔍 Đường dẫn cơ sở: {path_manager.get_base_path()}")
        print(f"🔍 Đường dẫn file license: {get_license_file_path()}")
        print(f"🔍 UUID máy: {machine_uuid}")

        # Kiểm tra file credentials
        # Ưu tiên 1: Nếu đang chạy từ file exe, tìm trong thư mục của file exe
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            possible_paths = [
                os.path.join(exe_dir, CREDENTIALS_FILE),
                os.path.join(exe_dir, "data", CREDENTIALS_FILE)
            ]
            print(f"🔍 Đang tìm file credentials trong thư mục exe: {exe_dir}")
        else:
            # Nếu không phải exe, sử dụng các đường dẫn khác
            possible_paths = []

        # Thêm các đường dẫn khác
        try:
            base_path = path_manager.get_base_path()
            possible_paths.extend([
                os.path.join(base_path, CREDENTIALS_FILE),
                os.path.join(base_path, "data", CREDENTIALS_FILE)
            ])
        except Exception as e:
            print(f"⚠️ Lỗi khi lấy base_path: {str(e)}")

        # Thêm đường dẫn tương đối với file hiện tại
        possible_paths.extend([
            os.path.join(os.path.dirname(os.path.abspath(__file__)), CREDENTIALS_FILE),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", CREDENTIALS_FILE)
        ])

        # Thêm đường dẫn trong AppData
        try:
            appdata = os.environ.get('APPDATA')
            if appdata:
                possible_paths.append(os.path.join(appdata, "AutoK", CREDENTIALS_FILE))
                possible_paths.append(os.path.join(appdata, "AutoK", "data", CREDENTIALS_FILE))
        except Exception as e:
            print(f"⚠️ Lỗi khi lấy đường dẫn AppData: {str(e)}")

        # Thêm đường dẫn hiện tại
        possible_paths.append(os.path.join(os.getcwd(), CREDENTIALS_FILE))
        possible_paths.append(os.path.join(os.getcwd(), "data", CREDENTIALS_FILE))

        print(f"🔍 Kiểm tra file credentials:")
        for path in possible_paths:
            if os.path.exists(path):
                print(f"  ✅ Tìm thấy tại: {path}")
            else:
                print(f"  ❌ Không tìm thấy tại: {path}")

        if not any(os.path.exists(path) for path in possible_paths):
            print(f"⚠️ Không tìm thấy file {CREDENTIALS_FILE} ở bất kỳ vị trí nào")
            print(f"⚠️ Vui lòng đảm bảo file {CREDENTIALS_FILE} được đặt trong thư mục data hoặc thư mục gốc của ứng dụng")

        # Không tạo license tạm thời nữa, yêu cầu kết nối internet
        return False, "Không thể kết nối đến máy chủ xác thực. Vui lòng kiểm tra kết nối internet và thử lại.", "", {}

    try:
        # In thông tin về sheet để debug
        print(f"🔍 Đang lấy dữ liệu từ Google Sheet...")

        records = sheet.get_all_records()
        print(f"✅ Đã lấy được {len(records)} bản ghi từ Google Sheet")

        # In ra các tiêu đề cột để debug
        headers = sheet.row_values(1)
        print(f"🔍 Tiêu đề các cột: {headers}")

        # Kiểm tra key trong records
        key_found = False
        for i, row in enumerate(records):
            if 'Key' in row and row['Key'] == key:
                key_found = True
                print(f"✅ Đã tìm thấy key trong Google Sheet (dòng {i+2})")
                print(f"🔍 Thông tin dòng: {row}")

                # Kiểm tra trạng thái
                if 'Status' in row:
                    status = row['Status'].lower()
                    print(f"🔍 Trạng thái: {status}")
                    if status != "active":
                        return False, "Key đã bị vô hiệu hóa", "", {}
                else:
                    print(f"⚠️ Không tìm thấy cột 'Status' trong dữ liệu")
                    # Thử tìm cột trạng thái với tên khác
                    status_found = False
                    for alt_name in ['status', 'Trạng thái', 'trang thai']:
                        if alt_name in row:
                            status = row[alt_name].lower()
                            print(f"🔍 Tìm thấy trạng thái trong cột '{alt_name}': {status}")
                            if status != "active":
                                return False, "Key đã bị vô hiệu hóa", "", {}
                            status_found = True
                            break

                    if not status_found:
                        print(f"⚠️ Không tìm thấy cột trạng thái với bất kỳ tên nào")

                # Kiểm tra ngày hết hạn
                try:
                    # Tìm cột ngày hết hạn với các tên có thể có
                    expiry_date_value = None
                    for date_col_name in ['Ngày Hết Hạn', 'Ngày hết hạn', 'Ngày Hết Hạng', 'Expiry Date']:
                        if date_col_name in row and row[date_col_name]:
                            expiry_date_value = row[date_col_name]
                            print(f"✅ Đã tìm thấy ngày hết hạn trong cột '{date_col_name}': {expiry_date_value}")
                            break

                    if not expiry_date_value:
                        print("⚠️ Không tìm thấy cột ngày hết hạn")
                        # Nếu không tìm thấy, bỏ qua kiểm tra này
                    else:
                        # Thử các định dạng ngày khác nhau
                        date_parsed = False
                        for date_format in ["%Y-%m-%d", "%d-%m-%Y", "%d/%m/%Y", "%Y/%m/%d"]:
                            try:
                                expiry_date = datetime.strptime(expiry_date_value, date_format)
                                print(f"✅ Đã phân tích ngày hết hạn thành công với định dạng {date_format}")
                                today = datetime.today()
                                if expiry_date < today:
                                    return False, "Key đã hết hạn sử dụng", "", {}

                                # Kiểm tra xem còn bao nhiêu ngày nữa hết hạn
                                days_left = (expiry_date - today).days
                                print(f"ℹ️ License còn {days_left} ngày nữa hết hạn")

                                # Nếu còn ít hơn hoặc bằng 3 ngày, hiển thị cảnh báo
                                if days_left <= 3:
                                    from PyQt5.QtWidgets import QMessageBox
                                    QMessageBox.warning(
                                        None,
                                        "License sắp hết hạn",
                                        f"License của bạn sẽ hết hạn sau {days_left} ngày nữa.\n\nVui lòng liên hệ Khương Nho để gia hạn."
                                    )

                                date_parsed = True
                                break
                            except ValueError:
                                continue

                        if not date_parsed:
                            print(f"⚠️ Không thể phân tích ngày hết hạn: {expiry_date_value}")
                except Exception as e:
                    print(f"⚠️ Lỗi khi kiểm tra ngày hết hạn: {str(e)}")
                    # Nếu không đọc được ngày hết hạn, bỏ qua kiểm tra này

                # Kiểm tra UUID
                uuid_value = ""
                for uuid_col_name in ['UUID', 'uuid', 'Uuid']:
                    if uuid_col_name in row and row[uuid_col_name]:
                        uuid_value = row[uuid_col_name]
                        print(f"✅ Đã tìm thấy UUID trong cột '{uuid_col_name}': {uuid_value}")
                        break

                if not uuid_value:
                    print("⚠️ Không tìm thấy cột UUID")
                    allowed_uuids = []
                else:
                    allowed_uuids = [u.strip() for u in uuid_value.split(',') if u.strip()]
                    print(f"🔍 Danh sách UUID được phép: {allowed_uuids}")

                # Tìm cột SL máy với các tên có thể có
                sl_may = 1  # Giá trị mặc định
                for sl_col_name in ['SL máy', 'SL', 'SL may', 'Số lượng máy']:
                    if sl_col_name in row and row[sl_col_name]:
                        try:
                            sl_may = int(row[sl_col_name])
                            print(f"✅ Đã tìm thấy SL máy trong cột '{sl_col_name}': {sl_may}")
                            break
                        except (ValueError, TypeError):
                            print(f"⚠️ Giá trị không hợp lệ trong cột '{sl_col_name}': {row[sl_col_name]}")
                            # Nếu không chuyển đổi được, sử dụng giá trị mặc định

                # Tìm cột Tính Năng với các tên có thể có
                features = "ALL"  # Giá trị mặc định
                for feature_col_name in ['Tính Năng', 'Tinh Nang', 'Features']:
                    if feature_col_name in row and row[feature_col_name]:
                        features = row[feature_col_name]
                        print(f"✅ Đã tìm thấy tính năng trong cột '{feature_col_name}': {features}")
                        break

                # Tìm ngày hết hạn để lưu
                expiry_date_str = "2099-12-31"  # Giá trị mặc định xa trong tương lai
                for date_col_name in ['Ngày Hết Hạn', 'Ngày hết hạn', 'Ngày Hết Hạng', 'Expiry Date']:
                    if date_col_name in row and row[date_col_name]:
                        expiry_date_str = row[date_col_name]
                        break

                if machine_uuid in allowed_uuids:
                    print(f"✅ UUID máy đã có trong danh sách được phép")
                    # Lưu license để sử dụng offline
                    save_license_offline(key, machine_uuid, features, expiry_date_str, row.get('User', 'AutoK User'), row.get('Email', '<EMAIL>'), sl_may)

                    # Tạo user_info từ dữ liệu Google Sheets
                    user_info = {
                        'user': row.get('User', 'AutoK User'),
                        'email': row.get('Email', '<EMAIL>'),
                        'plan': features,
                        'max_devices': sl_may,
                        'expires': expiry_date_str,
                        'key': key,
                        'uuid': machine_uuid
                    }

                    return True, "Key hợp lệ cho máy này", features, user_info

                elif len(allowed_uuids) < sl_may:
                    print(f"✅ Còn slot trống để thêm UUID mới (hiện tại: {len(allowed_uuids)}, tối đa: {sl_may})")
                    # Cập nhật UUID mới
                    allowed_uuids.append(machine_uuid)
                    new_uuids = ",".join(allowed_uuids)

                    # Tìm và cập nhật dòng
                    try:
                        cell = sheet.find(key)

                        # Lấy tiêu đề các cột để xác định vị trí cột UUID
                        headers = sheet.row_values(1)
                        uuid_col = None
                        for idx, header in enumerate(headers, 1):
                            if header == 'UUID':
                                uuid_col = idx
                                break

                        # Nếu không tìm thấy cột UUID, sử dụng giá trị mặc định
                        if uuid_col is None:
                            print(f"⚠️ Không tìm thấy cột UUID trong Google Sheet, sử dụng vị trí mặc định")
                            uuid_col = 6  # Điều chỉnh theo vị trí thực tế của cột UUID trong sheet

                        print(f"🔍 Cập nhật UUID mới vào cột {uuid_col}, dòng {cell.row}")
                        sheet.update_cell(cell.row, uuid_col, new_uuids)
                        print(f"✅ Đã cập nhật UUID mới vào Google Sheet: {new_uuids}")

                        # Gửi email thông báo
                        if 'User' in row and 'Email' in row:
                            send_activation_email(row['User'], row['Email'], key, machine_uuid)

                        # Lưu license để sử dụng offline
                        save_license_offline(key, machine_uuid, features, expiry_date_str, row.get('User', 'AutoK User'), row.get('Email', '<EMAIL>'), sl_may)

                        # Tạo user_info từ dữ liệu Google Sheets
                        user_info = {
                            'user': row.get('User', 'AutoK User'),
                            'email': row.get('Email', '<EMAIL>'),
                            'plan': features,
                            'max_devices': sl_may,
                            'expires': expiry_date_str,
                            'key': key,
                            'uuid': machine_uuid
                        }

                        return True, "Key hợp lệ, đã kích hoạt cho máy này", features, user_info
                    except Exception as e:
                        print(f"❌ Lỗi khi cập nhật UUID: {str(e)}")
                        return False, f"Lỗi khi kích hoạt key: {str(e)}", "", {}
                else:
                    print(f"❌ Key đã vượt quá số máy được phép kích hoạt ({len(allowed_uuids)}/{sl_may})")
                    return False, f"Key đã vượt quá số máy được phép kích hoạt ({sl_may} máy)", "", {}

                # Không cần break ở đây vì đã return trước đó

        if not key_found:
            print(f"❌ Không tìm thấy key trong Google Sheet")
            return False, "Key không tồn tại", "", {}

        # Nếu đến đây mà chưa return, có nghĩa là có lỗi logic
        print(f"⚠️ Lỗi logic không xác định trong quá trình kiểm tra license")
        return False, "Lỗi không xác định khi kiểm tra license", "", {}
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra license: {str(e)}")
        return False, f"Lỗi khi kiểm tra license: {str(e)}", "", {}

# === Lưu license để sử dụng offline ===
def save_license_offline(key, machine_uuid, features, expiry_date, user='AutoK User', email='<EMAIL>', max_devices=30):
    """Lưu thông tin license để có thể sử dụng offline"""
    try:
        license_data = {
            "key": key,
            "uuid": machine_uuid,
            "features": features,
            "expiry_date": expiry_date,
            "user": user,
            "email": email,
            "max_devices": max_devices,
            "activated_at": datetime.now().strftime("%Y-%m-%d")
        }

        # Mã hóa đơn giản
        license_str = json.dumps(license_data)
        encoded = ""
        for c in license_str:
            encoded += chr(ord(c) + 5)  # Shift mỗi ký tự lên 5 đơn vị

        # Lưu vào file
        license_path = get_license_file_path()

        # Đảm bảo thư mục tồn tại
        try:
            os.makedirs(os.path.dirname(license_path), exist_ok=True)
        except Exception as e:
            print(f"⚠️ Lỗi khi tạo thư mục cho license: {str(e)}")

            # Thử tạo trong AppData nếu không thể tạo ở vị trí hiện tại
            try:
                appdata = os.environ.get('APPDATA')
                if appdata:
                    alt_path = os.path.join(appdata, "AutoK", "data", "license.dat")
                    os.makedirs(os.path.dirname(alt_path), exist_ok=True)
                    license_path = alt_path
                    print(f"✅ Đã chuyển sang sử dụng đường dẫn thay thế: {license_path}")
            except Exception as e2:
                print(f"⚠️ Lỗi khi tạo thư mục thay thế: {str(e2)}")

        # Thử ghi file
        try:
            with open(license_path, "w", encoding="utf-8") as f:
                f.write(encoded)
            print(f"✅ Đã lưu thông tin license vào {license_path}")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi ghi file license: {str(e)}")

            # Thử ghi vào thư mục temp nếu không thể ghi vào vị trí hiện tại
            try:
                import tempfile
                temp_dir = os.path.join(tempfile.gettempdir(), "AutoK", "data")
                os.makedirs(temp_dir, exist_ok=True)
                temp_path = os.path.join(temp_dir, "license.dat")

                with open(temp_path, "w", encoding="utf-8") as f:
                    f.write(encoded)
                print(f"✅ Đã lưu thông tin license vào thư mục tạm: {temp_path}")
                return True
            except Exception as e2:
                print(f"❌ Lỗi khi ghi file license vào thư mục tạm: {str(e2)}")
                return False
    except Exception as e:
        print(f"❌ Lỗi khi lưu license offline: {str(e)}")
        return False

# === Kiểm tra license offline ===
def check_license_offline(key, machine_uuid):
    """
    Kiểm tra license từ file đã lưu trước đó

    Returns:
        tuple: (is_valid, message, features, user_info)
    """
    try:
        license_path = get_license_file_path()
        if not os.path.exists(license_path):
            print(f"ℹ️ Chưa có file license offline")
            return False, "Chưa có license được lưu", "", {}

        # Đọc và giải mã
        try:
            with open(license_path, "r", encoding="utf-8") as f:
                encoded = f.read()

            decoded = ""
            for c in encoded:
                decoded += chr(ord(c) - 5)  # Shift mỗi ký tự xuống 5 đơn vị

            license_data = json.loads(decoded)
        except Exception as e:
            print(f"❌ Lỗi khi đọc/giải mã file license: {str(e)}")
            # Không xóa file license.dat, chỉ trả về thông báo lỗi
            print(f"⚠️ File license có thể bị hỏng: {license_path}")
            print(f"⚠️ Chi tiết lỗi: {str(e)}")
            return False, "File license có thể bị hỏng, vui lòng kích hoạt lại", "", {}

        # Kiểm tra key và UUID
        if key and license_data["key"] != key:
            print(f"ℹ️ Key không khớp với license đã lưu")
            print(f"ℹ️ Key trong file: {license_data['key']}")
            print(f"ℹ️ Key đang kiểm tra: {key}")
            # Không xóa file license.dat, chỉ trả về thông báo lỗi
            return False, "Key không khớp với license đã lưu. Vui lòng kích hoạt lại.", "", {}

        if license_data["uuid"] != machine_uuid:
            print(f"ℹ️ UUID không khớp với license đã lưu. Có thể đã sao chép từ máy khác.")
            print(f"ℹ️ UUID trong file: {license_data['uuid']}")
            print(f"ℹ️ UUID máy hiện tại: {machine_uuid}")
            # Không xóa file license.dat, chỉ trả về thông báo lỗi
            return False, "Phát hiện sao chép từ máy khác. Vui lòng kích hoạt lại license cho máy này.", "", {}

        # Kiểm tra hạn sử dụng
        try:
            expiry_date = datetime.strptime(license_data["expiry_date"], "%Y-%m-%d")
            today = datetime.today()

            if expiry_date < today:
                print(f"ℹ️ License đã hết hạn")
                return False, "License đã hết hạn sử dụng", "", {}

            # Kiểm tra xem còn bao nhiêu ngày nữa hết hạn
            days_left = (expiry_date - today).days
            print(f"ℹ️ License còn {days_left} ngày nữa hết hạn")

            # Nếu còn ít hơn hoặc bằng 3 ngày, hiển thị cảnh báo
            if days_left <= 3:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(
                    None,
                    "License sắp hết hạn",
                    f"License của bạn sẽ hết hạn sau {days_left} ngày nữa.\n\nVui lòng liên hệ Khương Nho để gia hạn."
                )
                # Vẫn trả về True vì license vẫn còn hợp lệ
                # Tạo user_info từ license_data
                user_info = {
                    'user': license_data.get('user', 'AutoK User'),
                    'email': license_data.get('email', '<EMAIL>'),
                    'plan': license_data["features"],
                    'max_devices': license_data.get('max_devices', 30),
                    'expires': license_data["expiry_date"],
                    'key': license_data["key"],
                    'uuid': license_data["uuid"]
                }
                return True, f"License hợp lệ (còn {days_left} ngày nữa hết hạn)", license_data["features"], user_info
        except Exception as e:
            print(f"⚠️ Lỗi khi kiểm tra ngày hết hạn: {str(e)}")
            # Nếu không đọc được ngày hết hạn, bỏ qua kiểm tra này
            pass

        print(f"✅ Xác thực license offline thành công")
        # Tạo user_info từ license_data
        user_info = {
            'user': license_data.get('user', 'AutoK User'),
            'email': license_data.get('email', '<EMAIL>'),
            'plan': license_data["features"],
            'max_devices': license_data.get('max_devices', 30),
            'expires': license_data["expiry_date"],
            'key': license_data["key"],
            'uuid': license_data["uuid"]
        }
        return True, "License hợp lệ (offline)", license_data["features"], user_info
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra license offline: {str(e)}")
        return False, "Lỗi khi kiểm tra license offline", "", {}

# === Thử nghiệm ===
if __name__ == "__main__":
    print("=== AutoK License Manager ===")
    user_key = input("Nhập license key: ").strip()
    machine_uuid = get_machine_uuid()
    print("UUID của máy:", machine_uuid)

    valid, message, features = check_license(user_key, machine_uuid)
    if valid:
        print("✅ Key hợp lệ. Tính năng cho phép:", features)
    else:
        print("❌ Không dùng được:", message)
