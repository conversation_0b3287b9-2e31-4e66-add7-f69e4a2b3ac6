"""
Script để tạo file credentials mã hóa từ file JSON gốc của Google API
"""

import os
import sys
import crypto_utils

def main():
    if len(sys.argv) < 2:
        print("Sử dụng: python create_encrypted_credentials.py <input_json_file> [output_enc_file] [salt_file]")
        print("\nVí dụ:")
        print("python create_encrypted_credentials.py google_creds.json keytool-459101-41fc2d3fe493.enc keytool-salt.bin")
        return
    
    input_file = sys.argv[1]
    
    if not os.path.exists(input_file):
        print(f"❌ Không tìm thấy file đầu vào: {input_file}")
        return
    
    # Xác định tên file đầu ra
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    else:
        output_file = "keytool-459101-41fc2d3fe493.enc"
    
    # Xác định tên file salt
    if len(sys.argv) > 3:
        salt_file = sys.argv[3]
    else:
        salt_file = "keytool-salt.bin"
    
    # Mã hóa file
    if crypto_utils.encrypt_credentials(input_file, output_file, salt_file):
        print(f"✅ Đã mã hóa thành công file {input_file} thành {output_file}")
        print(f"✅ Salt đã được lưu vào file {salt_file}")
        print("\nLưu ý: Hãy giữ an toàn các file sau:")
        print(f"  1. {output_file} - File credentials đã mã hóa")
        print(f"  2. {salt_file} - File salt để giải mã")
        print("\nCả hai file này đều cần được phân phối cùng với ứng dụng.")
    else:
        print(f"❌ Mã hóa thất bại")

if __name__ == "__main__":
    main()
