<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>793</width>
    <height>461</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>793</width>
    <height>461</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>Icon/favicon.ico</normaloff>Icon/favicon.ico</iconset>
  </property>
  <property name="windowOpacity">
   <double>2.000000000000000</double>
  </property>
  <property name="styleSheet">
   <string notr="true">QLabel, QCheckBox {
    background-color: transparent;
}</string>
  </property>
  <property name="iconSize">
   <size>
    <width>48</width>
    <height>48</height>
   </size>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTabWidget" name="tabWidget_Proxy">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>661</width>
      <height>461</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Fixed" vsizetype="Expanding">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <weight>50</weight>
      <bold>false</bold>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">QTabBar::tab {
    background-color:#dfdfdf;
    color: black;
    padding: 2px 10px;
    height: 20px; /* thấp hơn mặc định */
    font-size: 11px;
    min-width: 80px;
    border-top-right-radius: 6px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background: #006241;
    color: white;
    font-weight: bold;
}

QTabWidget::pane {
    background-color: transparent;
    border: 1px solid #ccc;
    border-top: none;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

</string>
    </property>
    <property name="currentIndex">
     <number>0</number>
    </property>
    <widget class="QWidget" name="tab">
     <attribute name="title">
      <string>Auto_K</string>
     </attribute>
     <widget class="QTableWidget" name="tableWidget">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>661</width>
        <height>371</height>
       </rect>
      </property>
      <property name="maximumSize">
       <size>
        <width>661</width>
        <height>371</height>
       </size>
      </property>
      <property name="autoFillBackground">
       <bool>false</bool>
      </property>
      <property name="styleSheet">
       <string notr="true">
QTableWidget {
    background-color: white;
    gridline-color: #ccc;
    font-size: 14px;
}

QTableWidget::item {
    padding: 4px;
    color: black;
}

QTableWidget::item:selected {
    background-color: #a2d5f2;  /* Màu nền khi chọn */
    color: black;
}

QHeaderView::section {
    color: darkblue;
    background-color: #f0f0f0;
    font-weight: bold;
    padding: 4px;
    border: 1px solid #ccc;
}

</string>
      </property>
      <attribute name="horizontalHeaderCascadingSectionResizes">
       <bool>false</bool>
      </attribute>
      <attribute name="horizontalHeaderDefaultSectionSize">
       <number>80</number>
      </attribute>
      <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
       <bool>false</bool>
      </attribute>
      <attribute name="horizontalHeaderStretchLastSection">
       <bool>true</bool>
      </attribute>
      <attribute name="verticalHeaderCascadingSectionResizes">
       <bool>true</bool>
      </attribute>
      <column>
       <property name="text">
        <string/>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Devices</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Pass</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Authen</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Data</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Log</string>
       </property>
      </column>
     </widget>
     <widget class="QFrame" name="frame_9">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>370</y>
        <width>659</width>
        <height>71</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: #bfbfbf;  /* ví dụ: màu be nhạt */
</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <widget class="QGroupBox" name="groupBox_5">
       <property name="geometry">
        <rect>
         <x>288</x>
         <y>9</y>
         <width>371</width>
         <height>51</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
    border: 1.2px solid #8d8d8d;
    border-radius: 5px;
}
</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <widget class="QToolButton" name="toolButton_wallet">
        <property name="geometry">
         <rect>
          <x>328</x>
          <y>10</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/wallet.png</normaloff>
          <normalon>Icon/stop.png</normalon>
          <disabledoff>Icon/stop.png</disabledoff>Icon/wallet.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QToolButton" name="add_card_Button">
        <property name="geometry">
         <rect>
          <x>266</x>
          <y>10</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/ads.png</normaloff>
          <normalon>Icon/stop1.png</normalon>
          <disabledoff>Icon/stop1.png</disabledoff>Icon/ads.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QToolButton" name="pushButton_Mua">
        <property name="geometry">
         <rect>
          <x>79</x>
          <y>9</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/mua.png</normaloff>
          <normalon>Icon/stop1.png</normalon>
          <disabledoff>Icon/stop1.png</disabledoff>
          <activeoff>Icon/mua.png</activeoff>Icon/mua.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QToolButton" name="xoa_the_close_Button">
        <property name="geometry">
         <rect>
          <x>206</x>
          <y>9</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string>...</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/delete.png</normaloff>
          <normalon>Icon/stop1.png</normalon>
          <disabledoff>Icon/stop1.png</disabledoff>Icon/delete.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QToolButton" name="close_payment_Button">
        <property name="geometry">
         <rect>
          <x>143</x>
          <y>10</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/close.png</normaloff>
          <normalon>Icon/stop1.png</normalon>
          <disabledoff>Icon/stop1.png</disabledoff>Icon/close.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QToolButton" name="chplay">
        <property name="geometry">
         <rect>
          <x>13</x>
          <y>9</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/chplay.png</normaloff>
          <normalon>Icon/stop1.png</normalon>
          <disabledoff>Icon/stop1.png</disabledoff>Icon/chplay.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
        <property name="autoRepeat">
         <bool>false</bool>
        </property>
        <property name="autoExclusive">
         <bool>false</bool>
        </property>
        <property name="autoRaise">
         <bool>false</bool>
        </property>
       </widget>
      </widget>
      <widget class="QGroupBox" name="groupBox_6">
       <property name="geometry">
        <rect>
         <x>73</x>
         <y>9</y>
         <width>111</width>
         <height>51</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
    border: 1.2px solid #8d8d8d;
    border-radius: 5px;
}
</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <widget class="QToolButton" name="restore">
        <property name="geometry">
         <rect>
          <x>68</x>
          <y>10</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/restore.png</normaloff>
          <normalon>Icon/stop.png</normalon>Icon/restore.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QToolButton" name="changer">
        <property name="geometry">
         <rect>
          <x>12</x>
          <y>10</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/changer.png</normaloff>
          <normalon>Icon/stop.png</normalon>Icon/changer.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>25</width>
          <height>25</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </widget>
      <widget class="QGroupBox" name="groupBox_7">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>9</y>
         <width>51</width>
         <height>51</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
    border: 1.2px solid #8d8d8d;
    border-radius: 5px;
}
</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <widget class="QToolButton" name="button_wifi">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>10</y>
          <width>31</width>
          <height>31</height>
         </rect>
        </property>
        <property name="focusPolicy">
         <enum>Qt::WheelFocus</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">QToolButton {
    border: none;
    background-color: transparent;
}
QToolButton:checked {
    background-color: rgba(0, 255, 0, 15); /* nền rất nhẹ */
     border-radius: 20px; /* tùy chọn bo góc */
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>Icon/wifi-off.png</normaloff>
          <normalon>Icon/wifi.png</normalon>
          <disabledoff>Icon/wifi-off.png</disabledoff>
          <disabledon>Icon/wifi-off.png</disabledon>
          <activeoff>Icon/wifi-off.png</activeoff>
          <activeon>Icon/wifi.png</activeon>
          <selectedoff>Icon/wifi-off.png</selectedoff>
          <selectedon>Icon/wifi.png</selectedon>Icon/wifi-off.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </widget>
     </widget>
     <widget class="QCheckBox" name="checkBoxALL">
      <property name="geometry">
       <rect>
        <x>27</x>
        <y>1</y>
        <width>31</width>
        <height>31</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator {
    width: 0px;
    height: 0px;
}</string>
      </property>
      <property name="icon">
       <iconset>
        <normaloff>Icon/checkbox_off.png</normaloff>
        <normalon>Icon/checkbox_on.png</normalon>Icon/checkbox_off.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>21</width>
        <height>21</height>
       </size>
      </property>
      <property name="autoRepeatDelay">
       <number>300</number>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="tab_2">
     <attribute name="title">
      <string>Setting</string>
     </attribute>
     <widget class="QTableView" name="tableView">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>661</width>
        <height>431</height>
       </rect>
      </property>
     </widget>
     <widget class="QFrame" name="frame_5">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>661</width>
        <height>441</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: #ececec; </string>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Sunken</enum>
      </property>
      <widget class="QLabel" name="wide">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>44</y>
         <width>21</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QLabel" name="high">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>43</y>
         <width>21</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QGroupBox" name="groupBox">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>179</y>
         <width>641</width>
         <height>241</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
    border: 1.2px solid #c0c0c0;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px; /* khoảng cách cho tiêu đề */
    background-color: #f7f7f7;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
}</string>
       </property>
       <property name="title">
        <string>Setting auto</string>
       </property>
       <widget class="QComboBox" name="comboBox_4">
        <property name="geometry">
         <rect>
          <x>510</x>
          <y>182</y>
          <width>101</width>
          <height>22</height>
         </rect>
        </property>
        <item>
         <property name="text">
          <string>Mua Game</string>
         </property>
        </item>
       </widget>
       <widget class="QCheckBox" name="checkBox_mua">
        <property name="geometry">
         <rect>
          <x>479</x>
          <y>184</y>
          <width>16</width>
          <height>20</height>
         </rect>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QCheckBox" name="checkBox_xoa_close_payment">
        <property name="geometry">
         <rect>
          <x>480</x>
          <y>88</y>
          <width>16</width>
          <height>17</height>
         </rect>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QComboBox" name="comboBox_wallet_ads">
        <property name="geometry">
         <rect>
          <x>510</x>
          <y>134</y>
          <width>101</width>
          <height>22</height>
         </rect>
        </property>
        <item>
         <property name="text">
          <string>Add Wallet</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Add Ads</string>
         </property>
        </item>
       </widget>
       <widget class="QCheckBox" name="checkBox_restore_email">
        <property name="geometry">
         <rect>
          <x>480</x>
          <y>40</y>
          <width>16</width>
          <height>17</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    background: transparent;
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QComboBox" name="comboBox_restore">
        <property name="geometry">
         <rect>
          <x>510</x>
          <y>38</y>
          <width>101</width>
          <height>22</height>
         </rect>
        </property>
        <item>
         <property name="text">
          <string>Restore</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Changer</string>
         </property>
        </item>
       </widget>
       <widget class="QCheckBox" name="checkBox_add_wallet_ads">
        <property name="geometry">
         <rect>
          <x>480</x>
          <y>137</y>
          <width>16</width>
          <height>17</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    background: transparent;
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QComboBox" name="comboBox_del_close">
        <property name="geometry">
         <rect>
          <x>510</x>
          <y>84</y>
          <width>101</width>
          <height>22</height>
         </rect>
        </property>
        <item>
         <property name="text">
          <string>Xoá thẻ close</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Đóng hồ sơ</string>
         </property>
        </item>
       </widget>
       <widget class="QLabel" name="label_5">
        <property name="geometry">
         <rect>
          <x>240</x>
          <y>186</y>
          <width>101</width>
          <height>16</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    background: transparent;
}</string>
        </property>
        <property name="text">
         <string>Số lần add thẻ</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="goi1">
        <property name="geometry">
         <rect>
          <x>360</x>
          <y>39</y>
          <width>42</width>
          <height>22</height>
         </rect>
        </property>
        <property name="maximum">
         <number>15</number>
        </property>
        <property name="singleStep">
         <number>1</number>
        </property>
       </widget>
       <widget class="QSpinBox" name="goi2">
        <property name="geometry">
         <rect>
          <x>360</x>
          <y>74</y>
          <width>42</width>
          <height>22</height>
         </rect>
        </property>
        <property name="maximum">
         <number>15</number>
        </property>
        <property name="singleStep">
         <number>1</number>
        </property>
       </widget>
       <widget class="QLabel" name="label_4">
        <property name="geometry">
         <rect>
          <x>240</x>
          <y>41</y>
          <width>91</width>
          <height>16</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    background: transparent;
}</string>
        </property>
        <property name="text">
         <string>Chọn gói mua</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="goi3">
        <property name="geometry">
         <rect>
          <x>360</x>
          <y>108</y>
          <width>42</width>
          <height>22</height>
         </rect>
        </property>
        <property name="maximum">
         <number>50</number>
        </property>
       </widget>
       <widget class="QSpinBox" name="spinBox_so_lan_add_card">
        <property name="geometry">
         <rect>
          <x>360</x>
          <y>182</y>
          <width>41</width>
          <height>22</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">color: #000000;    </string>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>50</number>
        </property>
        <property name="singleStep">
         <number>1</number>
        </property>
        <property name="value">
         <number>3</number>
        </property>
       </widget>
      </widget>
      <widget class="QGroupBox" name="groupBox_3">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>4</y>
         <width>641</width>
         <height>171</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
    border: 1.2px solid #c0c0c0;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px; /* khoảng cách cho tiêu đề */
    background-color: #f7f7f7;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
}</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <widget class="QLabel" name="label">
        <property name="geometry">
         <rect>
          <x>172</x>
          <y>119</y>
          <width>61</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    background: transparent;
}</string>
        </property>
        <property name="text">
         <string>Port Doma </string>
        </property>
       </widget>
       <widget class="QLabel" name="label_6">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>34</y>
          <width>201</width>
          <height>16</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    background: transparent;
}</string>
        </property>
        <property name="text">
         <string>Kích thước View</string>
        </property>
       </widget>
       <widget class="QPushButton" name="open_mail_Button">
        <property name="geometry">
         <rect>
          <x>519</x>
          <y>73</y>
          <width>101</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
        </property>
        <property name="text">
         <string>EMail</string>
        </property>
       </widget>
       <widget class="QCheckBox" name="checkBox_backup">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>120</y>
          <width>70</width>
          <height>17</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel, QCheckBox {
    background-color: transparent;
}</string>
        </property>
        <property name="text">
         <string>Backup</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="lineEdit_port_doma">
        <property name="geometry">
         <rect>
          <x>242</x>
          <y>118</y>
          <width>51</width>
          <height>20</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QLineEdit {
    border: 1px solid #aaa;
    border-radius: 4px;     /* 👈 Bo góc */
    padding: 2px;
}
</string>
        </property>
       </widget>
       <widget class="QPushButton" name="pushButton">
        <property name="geometry">
         <rect>
          <x>519</x>
          <y>29</y>
          <width>101</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
        </property>
        <property name="text">
         <string>📂 Open Data</string>
        </property>
       </widget>
       <widget class="QPushButton" name="open_card_Button">
        <property name="geometry">
         <rect>
          <x>519</x>
          <y>116</y>
          <width>101</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
        </property>
        <property name="text">
         <string>Data</string>
        </property>
       </widget>
       <widget class="QPushButton" name="licenses">
        <property name="geometry">
         <rect>
          <x>19</x>
          <y>116</y>
          <width>91</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
        </property>
        <property name="text">
         <string>Licenses Info</string>
        </property>
       </widget>
       <widget class="QSlider" name="horizontalSlider">
        <property name="geometry">
         <rect>
          <x>200</x>
          <y>32</y>
          <width>131</width>
          <height>22</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">QSlider:focus {
    outline: none;
    border: none;
}

QSlider::groove:horizontal {
    border: 1px solid #00aa7f;
    height: 6px;
    background: #444;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background: #00aa7f;
    border: 1px solid #5c5c5c;
    width: 14px;
    margin: -4px 0; /* để align với groove */
    border-radius: 7px;
}
</string>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="tab_3">
     <attribute name="title">
      <string>Proxy</string>
     </attribute>
     <widget class="QGroupBox" name="groupBox_proxy">
      <property name="geometry">
       <rect>
        <x>9</x>
        <y>139</y>
        <width>641</width>
        <height>281</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    border: 1.2px solid #c0c0c0;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px; /* khoảng cách cho tiêu đề */
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 20  px;

}</string>
      </property>
      <property name="title">
       <string>Dvices gán proxy:</string>
      </property>
      <widget class="QListView" name="listView">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>30</y>
         <width>621</width>
         <height>241</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QListView {
    border: 1.2px solid #cccccc;
    border-radius: 10px;
    padding: 5px;
    background-color: white;
}</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_4">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>4</y>
        <width>641</width>
        <height>131</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    border: 1.2px solid #c0c0c0;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px; /* khoảng cách cho tiêu đề */
    background-color: #f7f7f7;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
}</string>
      </property>
      <property name="title">
       <string/>
      </property>
      <widget class="QPushButton" name="pushButton_8">
       <property name="geometry">
        <rect>
         <x>502</x>
         <y>95</y>
         <width>121</width>
         <height>23</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
       </property>
       <property name="text">
        <string>🛑 Open Chrome</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_5">
       <property name="geometry">
        <rect>
         <x>352</x>
         <y>23</y>
         <width>121</width>
         <height>23</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
       </property>
       <property name="text">
        <string>🧹 Dừng  Proxy</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_2">
       <property name="geometry">
        <rect>
         <x>352</x>
         <y>95</y>
         <width>121</width>
         <height>23</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
       </property>
       <property name="text">
        <string>🎯  Đổi Proxy</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_7">
       <property name="geometry">
        <rect>
         <x>502</x>
         <y>59</y>
         <width>121</width>
         <height>23</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    color: #F0F0F0;
    border: 1px solid #444;
    border-radius: 4px;
}

/* OFF (mặc định): màu đỏ */
QPushButton:!checked {
    background-color: #c0392b;
}

/* Hover khi OFF */
QPushButton:!checked:hover {
    background-color: #e74c3c;
}

/* ON (checked): màu xanh */
QPushButton:checked {
    background-color: rgb(16, 147, 99);
}

/* Hover khi ON */
QPushButton:checked:hover {
    background-color: #7c7c7c;
}

/* Khi nhấn */
QPushButton:pressed {
    background-color: #2e688d;
}
</string>
       </property>
       <property name="text">
        <string>🎨 Auto-Assign: ON</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_6">
       <property name="geometry">
        <rect>
         <x>502</x>
         <y>23</y>
         <width>121</width>
         <height>23</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
       </property>
       <property name="text">
        <string>📋 Config Proxy</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_4">
       <property name="geometry">
        <rect>
         <x>352</x>
         <y>59</y>
         <width>121</width>
         <height>23</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #939393;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 4px;             /* Bo góc nhẹ */
}

QPushButton:hover {
    background-color:#7c7c7c;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
       </property>
       <property name="text">
        <string>🧪 Check Proxy</string>
       </property>
      </widget>
      <widget class="QLabel" name="label_9">
       <property name="geometry">
        <rect>
         <x>13</x>
         <y>92</y>
         <width>231</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>Ports:</string>
       </property>
      </widget>
      <widget class="QLabel" name="label_8">
       <property name="geometry">
        <rect>
         <x>13</x>
         <y>69</y>
         <width>221</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>IP Sever :</string>
       </property>
      </widget>
     </widget>
    </widget>
   </widget>
   <widget class="QFrame" name="frame_7">
    <property name="geometry">
     <rect>
      <x>660</x>
      <y>0</y>
      <width>141</width>
      <height>461</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="minimumSize">
     <size>
      <width>141</width>
      <height>461</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>461</width>
      <height>461</height>
     </size>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: #bfbfbf;  /* ví dụ: màu be nhạt */
</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <widget class="QPushButton" name="paste_pass_Button">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>180</y>
       <width>101</width>
       <height>35</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #5b5b5b;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 8px;             /* Bo góc nhẹ */
    padding: 6px 12px;              /* Đệm */
    font-weight: bold;
}

QPushButton:hover {
    background-color: #919191;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
     </property>
     <property name="text">
      <string>Paste Pass</string>
     </property>
    </widget>
    <widget class="QPushButton" name="view_Button">
     <property name="geometry">
      <rect>
       <x>16</x>
       <y>112</y>
       <width>101</width>
       <height>27</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #5b5b5b;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 8px;             /* Bo góc nhẹ */
    padding: 6px 12px;              /* Đệm */
    font-weight: bold;
}

QPushButton:hover {
    background-color: #919191;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
     </property>
     <property name="text">
      <string>View</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pase_data_Button">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>272</y>
       <width>101</width>
       <height>35</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #5b5b5b;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 8px;             /* Bo góc nhẹ */
    padding: 6px 12px;              /* Đệm */
    font-weight: bold;
}

QPushButton:hover {
    background-color: #919191;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
     </property>
     <property name="text">
      <string>Paste Data</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pase_authen_Button">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>226</y>
       <width>101</width>
       <height>35</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #5b5b5b;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 8px;             /* Bo góc nhẹ */
    padding: 6px 12px;              /* Đệm */
    font-weight: bold;
}

QPushButton:hover {
    background-color: #919191;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
     </property>
     <property name="text">
      <string>Paste Authen</string>
     </property>
    </widget>
    <widget class="QCheckBox" name="checkBox_enter">
     <property name="geometry">
      <rect>
       <x>8</x>
       <y>150</y>
       <width>121</width>
       <height>17</height>
      </rect>
     </property>
     <property name="text">
      <string>Enter sau khi paste</string>
     </property>
    </widget>
    <widget class="QPushButton" name="home_Button">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>336</y>
       <width>101</width>
       <height>27</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #5b5b5b;      /* Xám đậm */
    color: #F0F0F0;                 /* Chữ trắng xám */
    border: 1px solid #444;         /* Viền xám than */
    border-radius: 8px;             /* Bo góc nhẹ */
    padding: 6px 12px;              /* Đệm */
    font-weight: bold;
}

QPushButton:hover {
    background-color: #919191;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
</string>
     </property>
     <property name="text">
      <string>🏠 Home</string>
     </property>
    </widget>
    <widget class="QPushButton" name="start_stop_Button">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>402</y>
       <width>101</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>20</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #007455;      /* Xám đậm */
    color: #f1f1f1;                 /* Chữ trắng xám */
    border: 1px solid #343434;         /* Viền xám than */
    border-radius: 8px;             /* Bo góc nhẹ */
    padding: 6px 12px;              /* Đệm */
    font-weight: bold;
}

QPushButton:hover {
    background-color: #00b383;      /* Hover sáng nhẹ */
}

QPushButton:pressed {
    background-color: #2e688d;      /* Nhấn: tối hơn */
}
QPushButton:checked {
    background-color: red;
}</string>
     </property>
     <property name="text">
      <string>Start</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="geometry">
      <rect>
       <x>6</x>
       <y>7</y>
       <width>121</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    border: 1.2px solid #8d8d8d;
    border-radius: 5px;
}
</string>
     </property>
     <property name="title">
      <string/>
     </property>
     <widget class="QLabel" name="label_Error">
      <property name="geometry">
       <rect>
        <x>75</x>
        <y>67</y>
        <width>41</width>
        <height>16</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">color: red;  </string>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_suscess">
      <property name="geometry">
       <rect>
        <x>5</x>
        <y>49</y>
        <width>47</width>
        <height>13</height>
       </rect>
      </property>
      <property name="text">
       <string>Success</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_errro">
      <property name="geometry">
       <rect>
        <x>5</x>
        <y>69</y>
        <width>47</width>
        <height>13</height>
       </rect>
      </property>
      <property name="text">
       <string>Error</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_total_card">
      <property name="geometry">
       <rect>
        <x>75</x>
        <y>5</y>
        <width>41</width>
        <height>16</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">color: #0f0f0f;</string>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_da_xu_ly">
      <property name="geometry">
       <rect>
        <x>5</x>
        <y>26</y>
        <width>51</width>
        <height>16</height>
       </rect>
      </property>
      <property name="text">
       <string>Đã xử lý</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_Total_card">
      <property name="geometry">
       <rect>
        <x>5</x>
        <y>5</y>
        <width>61</width>
        <height>16</height>
       </rect>
      </property>
      <property name="text">
       <string>Tổng số thẻ</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_processed">
      <property name="geometry">
       <rect>
        <x>75</x>
        <y>25</y>
        <width>41</width>
        <height>16</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">color:#9700e2;</string>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_Success">
      <property name="geometry">
       <rect>
        <x>75</x>
        <y>47</y>
        <width>41</width>
        <height>16</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">color:#004eeb;</string>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </widget>
    </widget>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
