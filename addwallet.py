"""
<PERSON><PERSON><PERSON><PERSON><PERSON> thêm ví (Add Wallet) cho AutoK
Thực hiện chuỗi hành động tự động để thêm ví vào Google Wallet
"""

import subprocess
import time
import random
import cv2
import os
import sys
import datetime

# Import path_manager để quản lý đường dẫn
import path_manager

class add_wallet:
    def __init__(self, serial, num_loops=1, device_index=0, total_devices=1, adb_path=None):
        """
        Khởi tạo đối tượng add_wallet.
        :param serial: Số serial của thiết bị.
        :param num_loops: Số lần lặp (mặc định là 1).
        :param device_index: Chỉ số thiết bị trong danh sách (bắt đầu từ 0).
        :param total_devices: Tổng số thiết bị.
        :param adb_path: Đường dẫn đến ADB (tùy chọn).
        """
        self.serial = serial
        self.num_loops = num_loops
        self.device_index = device_index
        self.total_devices = total_devices

        # Thiết lập ADB path
        if adb_path and adb_path != "adb":
            self.adb_path = adb_path
        else:
            # Thử tìm ADB path từ AutoK
            try:
                from AutoK import get_adb_path
                self.adb_path = get_adb_path()
            except:
                self.adb_path = "adb"  # Fallback

        # Lấy đường dẫn template
        self.template_dir = path_manager.get_template_path()

        # Biến để theo dõi trạng thái hiện tại cho UI update
        self.current_card = 0
        self.total_cards = 0
        self.current_card_info = None
        self.update_status_func = None

        # Đường dẫn đến các file template
        self.addwallet_template_path = os.path.join(self.template_dir, "addwallet.png")
        self.view_wallet_template_path = os.path.join(self.template_dir, "view_wallet.png")
        self.not_now_template_path = os.path.join(self.template_dir, "not_now.png")
        self.add_wallet_template_path = os.path.join(self.template_dir, "add_wallet.png")
        self.payment_card_template_path = os.path.join(self.template_dir, "payment_card.png")
        self.credit_wallet_template_path = os.path.join(self.template_dir, "credit_wallet.png")
        self.new_credit_wallet_template_path = os.path.join(self.template_dir, "new_credit_wallet.png")

        # Template cho nhập thẻ
        self.card_number_template_path = os.path.join(self.template_dir, "card_number.png")
        self.enter_details_template_path = os.path.join(self.template_dir, "enter_details.png")
        self.or_enter_template_path = os.path.join(self.template_dir, "or_enter.png")
        self.united_states_template_path = os.path.join(self.template_dir, "united_states.png")
        self.united_states2_template_path = os.path.join(self.template_dir, "united_states2.png")
        self.save_and_continue_template_path = os.path.join(self.template_dir, "save_and_continue.png")
        self.got_it_wallet_template_path = os.path.join(self.template_dir, "got_it_wallet.png")
        self.add_another_template_path = os.path.join(self.template_dir, "add_another.png")
        self.continue_template_path = os.path.join(self.template_dir, "continue.png")
        self.save_card_template_path = os.path.join(self.template_dir, "save_card.png")
        self.name_template_path = os.path.join(self.template_dir, "name.png")

    def _random_sleep(self, min_seconds, max_seconds, message=None, stop_flag_func=None):
        """Tạm dừng thực thi trong một khoảng thời gian ngẫu nhiên với khả năng dừng."""
        seconds = random.uniform(min_seconds, max_seconds)
        if message:
            print(f"{message} ({seconds:.2f}s)")

        # Sleep với khả năng kiểm tra cờ dừng
        if stop_flag_func:
            # Chia sleep thành các chunk nhỏ để có thể kiểm tra cờ dừng
            sleep_chunks = max(1, int(seconds * 10))  # 10 chunks per second
            chunk_time = seconds / sleep_chunks

            for i in range(sleep_chunks):
                if stop_flag_func and stop_flag_func():
                    print(f"⚠️ Đã nhận tín hiệu dừng trong khi sleep (chunk {i+1}/{sleep_chunks})")
                    return True  # Return True to indicate stop was detected
                time.sleep(chunk_time)

                # Update UI during sleep
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass
        else:
            time.sleep(seconds)

        return False  # Return False to indicate normal completion

    def press_key(self, keycode):
        """Gửi phím đến thiết bị."""
        try:
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "keyevent", str(keycode)]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
        except FileNotFoundError:
            print(f"❌ Không tìm thấy ADB tại: {self.adb_path}")
            raise
        except Exception as e:
            print(f"❌ Lỗi khi gửi phím: {e}")
            raise

    def tap(self, x, y):
        """Tap vào tọa độ x, y."""
        try:
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "tap", str(x), str(y)]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print(f"✅ Đã tap vào tọa độ ({x}, {y})")
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi khi tap vào ({x}, {y}): {e}")

    def swipe(self, start_x, start_y, end_x, end_y, duration=300):
        """Thực hiện thao tác vuốt trên màn hình."""
        try:
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "swipe",
                   str(start_x), str(start_y), str(end_x), str(end_y), str(duration)]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
        except FileNotFoundError:
            print(f"❌ Không tìm thấy ADB tại: {self.adb_path}")
            raise
        except Exception as e:
            print(f"❌ Lỗi khi vuốt màn hình: {e}")
            raise

    def take_screenshot(self):
        """Chụp ảnh màn hình và trả về đường dẫn file."""
        screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")
        try:
            cmd = [self.adb_path, "-s", self.serial, "exec-out", "screencap", "-p"]
            with open(screenshot_path, "wb") as f:
                subprocess.run(cmd, stdout=f, check=True,
                             creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return screenshot_path
        except FileNotFoundError:
            print(f"❌ Không tìm thấy ADB tại: {self.adb_path}")
            raise
        except Exception as e:
            print(f"❌ Lỗi khi chụp ảnh màn hình: {e}")
            raise

    def find_image_on_screen(self, template_path, threshold=0.8, max_retries=3):
        """Tìm hình ảnh trên màn hình nhưng không tap vào."""
        for attempt in range(max_retries):
            try:
                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Chụp ảnh màn hình
                screenshot_path = self.take_screenshot()

                # Đọc ảnh template và ảnh màn hình
                template = cv2.imread(template_path)
                screenshot = cv2.imread(screenshot_path)

                if template is None or screenshot is None:
                    print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình (lần thử {attempt + 1}/{max_retries})")
                    continue

                # Chuyển đổi ảnh sang thang độ xám
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

                # Tìm kiếm template trong ảnh màn hình
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                # Nếu độ khớp lớn hơn ngưỡng, trả về vị trí tìm thấy
                if max_val >= threshold:
                    # Tính toán tọa độ trung tâm của template
                    h, w = template_gray.shape
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2

                    print(f"✅ Đã tìm thấy {os.path.basename(template_path)} tại ({center_x}, {center_y}) (độ khớp: {max_val:.2f})")
                    return center_x, center_y
                else:
                    print(f"❌ Không tìm thấy {os.path.basename(template_path)} trong lần thử {attempt + 1}/{max_retries} (max_val = {max_val:.2f})")

            except Exception as e:
                print(f"❌ Lỗi khi tìm {os.path.basename(template_path)}: {str(e)}")
            return None

    def find_image_and_tap(self, template_path, threshold=0.8, max_retries=2):
        """Tìm hình ảnh trên màn hình và tap vào vị trí tìm thấy."""
        for attempt in range(max_retries):
            try:
                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Chụp ảnh màn hình
                screenshot_path = self.take_screenshot()

                # Đọc ảnh template và ảnh màn hình
                template = cv2.imread(template_path)
                screenshot = cv2.imread(screenshot_path)

                if template is None or screenshot is None:
                    print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình (lần thử {attempt + 1}/{max_retries})")
                    continue

                # Chuyển đổi ảnh sang thang độ xám
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

                # Tìm kiếm template trong ảnh màn hình
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
                if max_val >= threshold:
                    # Tính toán tọa độ trung tâm của template
                    h, w = template_gray.shape
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2

                    # Tap vào vị trí trung tâm
                    cmd = [self.adb_path, "-s", self.serial, "shell", "input", "tap",
                           str(center_x), str(center_y)]
                    subprocess.run(cmd, check=True,
                                 creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    print(f"✅ Đã tìm thấy và tap vào {os.path.basename(template_path)} (độ khớp: {max_val:.2f})")
                    return True
                else:
                    print(f"❌ Không tìm thấy {os.path.basename(template_path)} trong lần thử {attempt + 1}/{max_retries} (max_val = {max_val:.2f})")

            except Exception as e:
                print(f"❌ Lỗi khi tìm và tap vào {os.path.basename(template_path)}: {str(e)}")

            # Đợi một chút trước khi thử lại với UI update
            if attempt < max_retries - 1:
                self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                # Cập nhật UI trong khi chờ
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

        return False

    def get_next_card(self):
        """
        Lấy thẻ tiếp theo từ file data/card.txt
        :return: dict với card_number, month, year, cvv hoặc None nếu không có thẻ
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            if not os.path.exists(card_file_path):
                print(f"❌ Không tìm thấy file card.txt tại: {card_file_path}")
                return None

            # Đọc file hiện tại
            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Tìm thẻ đầu tiên khả dụng
            card_found = None
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                parts = line.split('|')
                if len(parts) < 4:
                    continue

                card_number = parts[0]
                month = parts[1]
                year = parts[2]
                cvv_status = parts[3]

                # Kiểm tra nếu thẻ khả dụng (chỉ kiểm tra -ok, -error, -expired)
                if (not cvv_status.endswith('-ok') and
                    not cvv_status.endswith('-error') and
                    not cvv_status.endswith('-expired')):

                    # Tách CVV và status (loại bỏ các suffix cũ nếu có)
                    if '-' in cvv_status:
                        cvv = cvv_status.split('-')[0]
                    else:
                        cvv = cvv_status

                    # Kiểm tra xem thẻ có hết hạn hay không
                    if self.is_card_expired(month, year):
                        print(f"⚠️ Thẻ {card_number[-4:]} đã hết hạn (tháng {month}/{year})")
                        # Đánh dấu thẻ hết hạn trong file ngay lập tức
                        temp_lines = lines.copy()
                        temp_lines[i] = f"{card_number}|{month}|{year}|{cvv}-expired\n"
                        try:
                            with open(card_file_path, 'w', encoding='utf-8') as temp_f:
                                temp_f.writelines(temp_lines)
                            print(f"✅ Đã đánh dấu thẻ {card_number[-4:]} hết hạn trong file")
                        except Exception as e:
                            print(f"⚠️ Không thể cập nhật file cho thẻ hết hạn: {e}")
                        continue  # Tìm thẻ tiếp theo

                    card_found = {
                        'card_number': card_number,
                        'month': month,
                        'year': year,
                        'cvv': cvv,
                        'line_index': i,
                        'original_line': line
                    }
                    break

            if not card_found:
                print("⚠️ Không tìm thấy thẻ nào khả dụng trong file card.txt")
                return None

            # Cập nhật thông tin thẻ hiện tại
            self.current_card_info = card_found

            # Trả về thẻ tìm thấy mà không cần đánh dấu -using
            print(f"✅ Lấy thẻ: {card_found['card_number'][:4]}****{card_found['card_number'][-4:]} {card_found['month']}/{card_found['year']}")
            return card_found

        except Exception as e:
            print(f"❌ Lỗi khi đọc file card.txt: {e}")
            return None

    def mark_card_used(self, card_info, success=True):
        """
        Đánh dấu thẻ đã được sử dụng trong file card.txt
        :param card_info: dict thông tin thẻ từ get_next_card()
        :param success: True nếu thành công, False nếu thất bại
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            # Đọc tất cả các dòng
            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Cập nhật dòng tương ứng
            line_index = card_info['line_index']
            if line_index < len(lines):
                parts = lines[line_index].strip().split('|')
                if len(parts) >= 4:
                    # Cập nhật status đơn giản
                    if success:
                        new_status = f"{card_info['cvv']}-ok"
                    else:
                        new_status = f"{card_info['cvv']}-error"

                    # Tạo dòng mới
                    new_line = f"{parts[0]}|{parts[1]}|{parts[2]}|{new_status}\n"
                    lines[line_index] = new_line

                    # Ghi lại file
                    with open(card_file_path, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    status_text = "thành công" if success else "thất bại"
                    print(f"✅ Đã đánh dấu thẻ {card_info['card_number'][-4:]} {status_text}")

                    # Cập nhật thống kê thẻ ngay lập tức
                    self.update_card_stats_realtime(card_info['card_number'][-4:], status_text)

        except Exception as e:
            print(f"❌ Lỗi khi cập nhật file card.txt: {e}")

    def update_card_stats_realtime(self, card_last4, status):
        """
        Cập nhật thống kê thẻ theo thời gian thực giống addads
        :param card_last4: 4 số cuối của thẻ
        :param status: Trạng thái thẻ (thành công/thất bại)
        """
        try:
            # Tìm cửa sổ chính của ứng dụng
            from PyQt5.QtWidgets import QApplication
            print(f"📊 Đang cập nhật thống kê thẻ sau khi đánh dấu thẻ {card_last4} là {status}...")

            # Đợi một chút để đảm bảo file đã được ghi hoàn tất
            import time
            time.sleep(0.5)

            # Tìm cửa sổ chính và cập nhật thống kê thẻ
            for widget in QApplication.topLevelWidgets():
                if hasattr(widget, 'update_card_stats'):
                    success = widget.update_card_stats()
                    if success:
                        print(f"✅ Đã cập nhật thống kê thẻ thành công sau khi đánh dấu thẻ {card_last4}")
                    else:
                        print(f"⚠️ Cập nhật thống kê thẻ không thành công sau khi đánh dấu thẻ {card_last4}")
                    break
        except Exception as e:
            print(f"❌ Lỗi khi cập nhật thống kê thẻ: {str(e)}")

    def is_card_expired(self, month, year):
        """
        Kiểm tra xem thẻ có quá hạn hay không
        :param month: Tháng hết hạn
        :param year: Năm hết hạn
        :return: True nếu thẻ đã quá hạn, False nếu thẻ còn hạn
        """
        try:
            # Lấy thời gian hiện tại
            now = datetime.datetime.now()
            current_month = now.month
            current_year = now.year

            # Chuyển đổi tháng và năm hết hạn sang số nguyên
            exp_month = int(month)

            # Xử lý năm 2 chữ số
            if len(year) == 2:
                exp_year = 2000 + int(year)
            else:
                exp_year = int(year)

            # Kiểm tra xem thẻ có quá hạn hay không
            if exp_year < current_year:
                return True
            elif exp_year == current_year and exp_month < current_month:
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra thẻ quá hạn: {str(e)}")
            # Nếu có lỗi, coi như thẻ không quá hạn
            return False

    def mark_card_expired(self, card_info):
        """
        Đánh dấu thẻ đã hết hạn trong file card.txt
        :param card_info: dict thông tin thẻ từ get_next_card()
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            # Đọc tất cả các dòng
            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Cập nhật dòng tương ứng
            line_index = card_info['line_index']
            if line_index < len(lines):
                parts = lines[line_index].strip().split('|')
                if len(parts) >= 4:
                    # Đánh dấu thẻ hết hạn
                    new_status = f"{card_info['cvv']}-expired"

                    # Tạo dòng mới
                    new_line = f"{parts[0]}|{parts[1]}|{parts[2]}|{new_status}\n"
                    lines[line_index] = new_line

                    # Ghi lại file
                    with open(card_file_path, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    print(f"⚠️ Đã đánh dấu thẻ {card_info['card_number'][-4:]} hết hạn")

        except Exception as e:
            print(f"❌ Lỗi khi đánh dấu thẻ hết hạn: {e}")

    def update_status_realtime(self):
        """
        Cập nhật trạng thái theo thời gian thực lên UI giống addads
        """
        try:
            # Gọi hàm cập nhật trạng thái nếu có
            if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                self.update_status_func()
                # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                self.update_status_func()
            else:
                from PyQt5.QtWidgets import QApplication
                QApplication.processEvents()
                # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                QApplication.processEvents()
        except:
            pass

    def check_wallet_result(self, stop_flag_func=None):
        """
        Kiểm tra kết quả thêm thẻ vào Google Wallet.
        Luôn tìm cả got_it_wallet.png và add_another.png mỗi lần gọi.
        Trả về True nếu tìm thấy got_it_wallet, False nếu thấy add_another hoặc không thấy gì.
        """
        try:
            print("🔍 Kiểm tra kết quả thêm thẻ vào Google Wallet...")

            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi kiểm tra kết quả")
                return False

            # Chờ kết quả hiện lên
            if self._random_sleep(13.0, 14.0, "⏳ Chờ kết quả thêm thẻ xuất hiện", stop_flag_func):
                return False

            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng sau khi chờ")
                return False

            # Ưu tiên thành công nếu tìm thấy cả hai (dù hiếm)
            found_success = self.find_image_and_tap(self.got_it_wallet_template_path)
            found_failure = self.find_image_and_tap(self.add_another_template_path)

            if found_success:
                print("✅ Tìm thấy got_it_wallet.png - Thêm thẻ thành công!")
                self._random_sleep(5.0, 6.0, "Chờ sau khi tap got_it_wallet")
                return True

            if found_failure:
                print("❌ Tìm thấy add_another.png - Thêm thẻ thất bại!")
                self._random_sleep(5.0, 6.0, "Chờ sau khi tap add_another")
                return False

            print("❌ Không tìm thấy got_it_wallet.png hoặc add_another.png - Thêm thẻ thất bại")
            return False

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra kết quả: {e}")
            return False

        
    def input_basic_card_info(self, card_info, stop_flag_func=None):
        """
        Nhập thông tin cơ bản của thẻ (số thẻ, tháng/năm, CVV)
        :param card_info: dict thông tin thẻ
        :param stop_flag_func: Function để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"📝 Bắt đầu nhập thông tin cơ bản thẻ {card_info['card_number'][-4:]}")

            # Bước 1: Nhập số thẻ (form đã tự động focus vào trường số thẻ)
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập số thẻ")
                return False

            print(f"⌨️ Bước 1: Nhập số thẻ: {card_info['card_number']}")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", card_info['card_number']]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            if self._random_sleep(2.0, 3.0, "Chờ form tự động chuyển sang trường tháng/năm", stop_flag_func):
                return False  # Stop detected during sleep

            # Bước 2: Nhập tháng/năm (form tự động chuyển)
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập tháng/năm")
                return False

            print(f"⌨️ Bước 2: Nhập tháng/năm: {card_info['month']}/{card_info['year']}")
            month_year = f"{card_info['month'].zfill(2)}/{card_info['year']}"
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", month_year]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            if self._random_sleep(2.0, 3.0, "Chờ form tự động chuyển sang trường CVV", stop_flag_func):
                return False  # Stop detected during sleep

            # Bước 3: Nhập CVV (form tự động chuyển)
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập CVV")
                return False

            print(f"⌨️ Bước 3: Nhập CVV: {card_info['cvv']}")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", card_info['cvv']]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            if self._random_sleep(2.0, 3.0, "Chờ form tự động chuyển sang trường name", stop_flag_func):
                return False  # Stop detected during sleep

            print(f"✅ Đã nhập xong thông tin cơ bản thẻ {card_info['card_number'][-4:]}")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi nhập thông tin cơ bản thẻ: {e}")
            return False

    def find_and_tap_enter_details(self):
        """
        Tìm và tap vào enter_details.png hoặc or_enter.png
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🔍 Đang tìm nút enter details hoặc or enter trên màn hình...")

            found = False

            if self.find_image_and_tap(self.enter_details_template_path):
                print("✅ Đã tìm thấy và tap vào enter_details.png")
                return True

            if self.find_image_and_tap(self.or_enter_template_path):
                print("✅ Đã tìm thấy và tap vào or_enter.png")
                return True

            print("⚠️ Không tìm thấy enter_details.png hoặc or_enter.png trên màn hình")
            return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm enter details: {e}")
            return False

    
    
    def input_card_info(self, card_info, stop_flag_func=None):
        """
        Nhập thông tin thẻ vào form với tất cả các bước bổ sung
        :param card_info: dict thông tin thẻ
        :param stop_flag_func: Function để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"📝 Bắt đầu nhập thông tin thẻ {card_info['card_number'][-4:]}")

            # Kiểm tra cờ dừng trước khi bắt đầu
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập thông tin thẻ")
                return False

            # Bước 1: Nhập name (form tự động chuyển sang ô name)
            print(f"⌨️ Nhập name: abc")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", "abc"]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            self._random_sleep(1.0, 2.0, "Chờ sau khi nhập name")

            # Bước 2: Tab 1 lần để chuyển trường
            print(f"⌨️ Tab 1 lần để chuyển trường")
            self.press_key(61)  # KEYCODE_TAB
            self._random_sleep(1.0, 2.0, "Chờ sau khi tab")

            # Bước 3: Kiểm tra united_states.png
            print(f"🔍 Bước 6: Kiểm tra united_states.png")
            united_states_found = False

            if os.path.exists(self.united_states_template_path):
                if self.find_image_on_screen(self.united_states_template_path):
                    print("✅ Tìm thấy united_states.png - Bỏ qua tap vị trí 500,937")
                    united_states_found = True
                else:
                    print("⚠️ Không tìm thấy united_states.png - Sẽ tap vào vị trí 500,937")
            else:
                print("⚠️ Template united_states.png không có - Sẽ tap vào vị trí 500,937")

            # Chỉ tap vào vị trí 500,937 nếu KHÔNG tìm thấy united_states.png
            if not united_states_found:
                print(f"👆 Tap vào vị trí 500,937 (vì không tìm thấy united_states.png)")
                self.tap(500, 937)
                self._random_sleep(1.0, 2.0, "Chờ sau khi tap vị trí 500,937")

                # Bước 3.1: Vuốt lên 10 lần thật mạnh cho hết list (chỉ khi đã tap vị trí)
                print(f"⬆️ Bước 3.1: Vuốt lên 10 lần thật mạnh cho hết list")
                for i in range(10):
                    self.swipe(540, 800, 490, 200, 80)  # Vuốt mạnh từ dưới lên trên
                    print(f"   - Vuốt lần {i+1}/10")
                    self._random_sleep(0.7, 1.5, f"Chờ sau vuốt lần {i+1}")

                # Bước 3.2: Vuốt từ trên xuống 2 lần (chỉ khi đã tap vị trí)
                print(f"⬇️ Bước 8: Vuốt xuống 2 lần vừa đủ")
                for i in range(2):
                    self.swipe(510, 160, 540, 770, 600)  # Vuốt vừa từ trên xuống dưới
                    print(f"   - Vuốt vừa lần {i+1}/2")
                    self._random_sleep(0.5, 1.0, f"Chờ sau vuốt vừa lần {i+1}")

                # Chờ 3-4 giây sau bước 8 trước khi tìm united_states2
                self._random_sleep(3.0, 4.0, "Chờ 3-4s sau bước 8 trước khi tìm united_states2")

                # Bước 3.3: Tìm và tap vào united_states2.png (chỉ khi đã tap vị trí)
                print(f"🔍 Bước 9: Tìm và tap vào united_states2.png")
                if os.path.exists(self.united_states2_template_path):
                    if self.find_image_and_tap(self.united_states2_template_path):
                        print("✅ Đã tìm thấy và tap vào united_states2.png")
                    else:
                        print("❌ Không tìm thấy united_states2.png")
                        return False
                else:
                    print("❌ Template united_states2.png không có")
                    return False

                self._random_sleep(1.0, 2.0, "Chờ sau khi chọn country")
            else:
                print("ℹ️ Bỏ qua bước 7, 8, 9 vì đã tìm thấy united_states.png")
                self._random_sleep(1.0, 2.0, "Chờ sau khi tìm thấy united_states")

            # Bước 4: Tab 2 lần để chuyển trường
            print(f"⌨️ Tab 2 lần để chuyển trường")
            for i in range(2):
                self.press_key(61)  # KEYCODE_TAB
                print(f"   - Tab lần {i+1}/2")
                self._random_sleep(0.5, 1.0, f"Chờ sau tab lần {i+1}")

            # Bước 5: Nhập 2 số bất kỳ
            print(f"⌨️ Nhập 2 số bất kỳ: 12")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", "12"]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            self._random_sleep(4.0, 5.0, "Chờ sau khi nhập 2 số")

            # Bước 6: Tap vào vị trí 505, 650
            print(f"👆 Tap vào vị trí 505, 650")
            self.tap(505, 650)
            self._random_sleep(2.0, 3.0, "Chờ sau khi tap vị trí 505, 650")
                       

        except Exception as e:
            print(f"❌ Lỗi khi nhập thông tin thẻ: {e}")
            return False

    def add_single_card(self, stop_flag_func=None):
        """
        Thêm một thẻ vào Google Wallet (từ bước credit_wallet.png đến khi cập nhật thẻ thành công)
        :param stop_flag_func: Function để kiểm tra cờ dừng (optional)
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🔄 Bắt đầu quy trình thêm một thẻ (new_credit_wallet → cập nhật thẻ)...")

            # Bước 1 Tìm và tap vào credit_wallet.png
            print("Bước 1: Tìm và tap vào credit_wallet.png")
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi tap credit_wallet")
                return False
            
            if self.find_image_and_tap(self.credit_wallet_template_path):
                print("✅ Đã tìm thấy và tap vào credit_wallet.png")
                self._random_sleep(2.0, 3.0, "Chờ sau khi tap credit_wallet")
            else:
                print("❌ Không tìm thấy credit_wallet.png, vẫn tiếp tục...")
            
            # Bước 1.2: Tìm tap vào payment_card.png
            print("Bước 1.2: Tìm và tap vào payment_card.png")
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi tap payment_card")
                return False

            if self.find_image_and_tap(self.payment_card_template_path):
                print("✅ Đã tìm thấy và tap vào payment_card.png")
                self._random_sleep(2.0, 3.0, "Chờ sau khi tap payment_card")
            else:
                print("❌ Không tìm thấy payment_card.png, vẫn tiếp tục...")

            # Bước 2: Tìm và tap vào new_credit_wallet.png (với timing riêng)
            print("Bước 2: Tìm và tap vào new_credit_wallet.png")
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi tap new_credit_wallet")
                return False

            if self.find_image_and_tap(self.new_credit_wallet_template_path):
                print("✅ Đã tìm thấy và tap vào new_credit_wallet.png")
                # Thời gian chờ riêng cho new_credit_wallet trong vòng lặp
                self._random_sleep(10.0, 12.0, "Chờ sau khi tap new_credit_wallet trong vòng lặp")
            else:
                print("❌ Không tìm thấy new_credit_wallet.png, vẫn tiếp tục...")
                

            # Bước 3: Tìm và tap vào enter_details.png hoặc or_enter.png
            print("🔍 Bước 3: Tìm và tap vào enter_details.png hoặc or_enter.png")

            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi bắt đầu tìm enter_details")
                return False

            max_attempts = 5
            for attempt in range(max_attempts):
                if stop_flag_func and stop_flag_func():
                    print(f"⚠️ Đã nhận tín hiệu dừng trong lần thử {attempt + 1}/{max_attempts}")
                    return False

                print(f"🔄 Thử tìm enter_details lần {attempt + 1}/{max_attempts}...")
                if self.find_and_tap_enter_details():
                    print("✅ Đã tìm thấy và tap vào enter_details hoặc or_enter")
                    self._random_sleep(3.0, 4.0, "Chờ sau khi tap enter details")
                    break
                else:
                    print(f"❌ Không tìm thấy enter_details.png hoặc or_enter.png (lần {attempt + 1})")
                    if attempt < max_attempts - 1:
                        self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                    else:
                        print("⛔️ Đã thử 5 lần nhưng không tìm thấy enter_details hoặc or_enter")


            # Bước 4: Lấy thẻ tiếp theo từ file
            print("Bước 4: Lấy thẻ tiếp theo từ file data/card.txt")
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi lấy thẻ")
                return False

            card_info = self.get_next_card()
            if not card_info:
                print("⚠️ Không có thẻ nào khả dụng để nhập")
                return False

            # Bước 4.2: Nhập thông tin thẻ và kiểm tra kết quả
            print(f"Bước 5: Nhập thông tin thẻ {card_info['card_number'][-4:]}")
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập thẻ")
                return False

            card_success = self.input_basic_card_info(card_info, stop_flag_func)

            # Bước 5 Kiểm tra ô name có xuất hiện không. Nếu có thực hiện bước input_card_info()
            if os.path.exists(self.name_template_path):
                if self.find_image_on_screen(self.name_template_path):
                    print("✅ Đã tìm thấy ô name, thực hiện nhập thông tin thẻ")
                    if stop_flag_func and stop_flag_func():
                        print("⚠️ Đã nhận tín hiệu dừng trước khi nhập thông tin thẻ")
                        return False

                    card_success = self.input_card_info(card_info, stop_flag_func)
                else:
                    print("❌ Không tìm thấy ô name, bỏ qua bước nhập thông tin thẻ")

            # Bước 5.2: Tìm và tap vào save_and_continue.png
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi tap save_and_continue")
                return False

            print(f"🔍 Tìm và tap vào save_and_continue.png")
            if os.path.exists(self.save_and_continue_template_path):
                if self.find_image_and_tap(self.save_and_continue_template_path):
                    print("✅ Đã tìm thấy và tap vào save_and_continue.png")
                    self._random_sleep(2.0, 3.0, "Chờ sau khi save and continue")

                    # Bước 14: Kiểm tra kết quả thêm thẻ
                    if stop_flag_func and stop_flag_func():
                        print("⚠️ Đã nhận tín hiệu dừng trước khi kiểm tra kết quả")
                        return False

                    print(f"🔍 Bước 14: Kiểm tra kết quả thêm thẻ")
                    wallet_success = self.check_wallet_result(stop_flag_func)

                    return wallet_success
                else:
                    print("❌ Không tìm thấy save_and_continue.png")
                    return False
       

            # Bước 6: Đánh dấu thẻ đã sử dụng (cập nhật thẻ thành công/thất bại)
            print("Bước 6: Cập nhật kết quả thẻ")
            if card_success:
                self.mark_card_used(card_info, success=True)
                print(f"✅ Đã cập nhật thẻ {card_info['card_number'][-4:]} thành công")
                return True
            else:
                self.mark_card_used(card_info, success=False)
                print(f"❌ Cập nhật thẻ {card_info['card_number'][-4:]} thất bại")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi thêm thẻ: {str(e)}")
            return False

    def execute_actions(self, stop_flag_func=None, num_cards=1):
        """
        Thực hiện chuỗi hành động thêm ví.
        :param stop_flag_func: Function để kiểm tra cờ dừng (optional)
        :param num_cards: Số lượng thẻ cần thêm (từ spinbox)
        :return: True nếu thành công, False nếu thất bại.
        """
        try:
            print(f"\n=== Bắt đầu thực hiện chuỗi hành động thêm {num_cards} thẻ vào ví trên thiết bị {self.serial} ===\n")

            # Kiểm tra các file template cần thiết
            missing_templates = []
            for template_name in ["addwallet.png", "view_wallet.png", "not_now.png", "add_wallet.png",
                                 "payment_card.png", "credit_wallet.png", "new_credit_wallet.png",
                                 "enter_details.png", "or_enter.png", "united_states.png", "united_states2.png",
                                 "save_and_continue.png", "got_it_wallet.png", "add_another.png",
                                 "continue.png", "save_card.png, name.png"]:
                template_path = os.path.join(self.template_dir, template_name)
                if not os.path.exists(template_path):
                    missing_templates.append(template_name)

            if missing_templates:
                print(f"⚠️ Không tìm thấy các file template sau: {', '.join(missing_templates)}")
                print(f"⚠️ Vui lòng đặt các file template vào thư mục {self.template_dir}")

            # Bước 1: Về màn hình chính
            print("Bước 1: Về màn hình chính")
            self.press_key(3)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")
            self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính")

            # Bước 2: Vuốt lên để hiển thị menu ứng dụng
            print("Bước 2: Vuốt lên để hiển thị menu ứng dụng")
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt từ giữa màn hình lên trên
            self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 3, 300)
            print("✅ Đã vuốt lên để vào menu ứng dụng")
            self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt vào menu ứng dụng")

            # Bước 3: Tìm Google Wallet
            print("Bước 3: Tìm và tap vào biểu tượng Google Wallet")
            wallet_found = False
            max_swipe_attempts = 3  # Tối đa 3 lần vuốt để tìm

            for attempt in range(max_swipe_attempts):
                # Kiểm tra cờ dừng
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng - Dừng tìm kiếm Google Wallet")
                    return False

                print(f"🔍 Lần thử {attempt + 1}/{max_swipe_attempts}: Tìm kiếm Google Wallet...")

                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Thử tìm addwallet.png
                if os.path.exists(self.addwallet_template_path):
                    print(f"🔍 Tìm kiếm addwallet.png...")
                    wallet_found = self.find_image_and_tap(self.addwallet_template_path)
                    if wallet_found:
                        print(f"✅ Đã tìm thấy và tap vào addwallet.png ở lần thử {attempt + 1}")
                        break

                # Nếu không tìm thấy, thử tìm wallet.png (nếu có)
                wallet_template_path = os.path.join(self.template_dir, "wallet.png")
                if not wallet_found and os.path.exists(wallet_template_path):
                    print(f"🔍 Tìm kiếm wallet.png...")
                    wallet_found = self.find_image_and_tap(wallet_template_path)
                    if wallet_found:
                        print(f"✅ Đã tìm thấy và tap vào wallet.png ở lần thử {attempt + 1}")
                        break

                # Nếu chưa tìm thấy và chưa phải lần cuối, vuốt lên để tìm tiếp
                if not wallet_found and attempt < max_swipe_attempts - 1:
                    print(f"⚠️ Chưa tìm thấy Google Wallet, vuốt lên để tìm tiếp...")
                    self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 3, 300)
                    print(f"✅ Đã vuốt lên lần {attempt + 2}")
                    self._random_sleep(1.0, 2.0, f"Chờ sau khi vuốt lần {attempt + 2}")
                    # Cập nhật UI sau khi vuốt
                    try:
                        from PyQt5.QtWidgets import QApplication
                        QApplication.processEvents()
                    except:
                        pass
                elif not wallet_found:
                    print(f"⚠️ Đã thử {max_swipe_attempts} lần vuốt nhưng vẫn chưa tìm thấy Google Wallet")

            # Nếu vẫn không tìm thấy Google Wallet, dừng lại
            if not wallet_found:
                print("❌ Không tìm thấy biểu tượng Google Wallet sau khi thử tìm kiếm")
                return False

            # Đợi ứng dụng khởi động
            self._random_sleep(12.0, 13.0, "Chờ Google Wallet khởi động hoàn toàn")

            # Bước 4-6: Thực hiện các bước setup (không bao gồm new_credit_wallet)
            steps = [
                ("view_wallet.png", "Bước 4: Tìm và tap vào view_wallet.png"),
                ("not_now.png", "Bước 5: Tìm và tap vào not_now.png (nếu xuất hiện)"),
                ("add_wallet.png", "Bước 6: Tìm và tap vào add_wallet.png")               
            ]

            for template_name, step_description in steps:
                # Kiểm tra cờ dừng
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng - Dừng thực hiện các bước tiếp theo")
                    return False

                print(step_description)

                # Cập nhật UI trước mỗi bước
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                template_path = os.path.join(self.template_dir, template_name)

                if os.path.exists(template_path):
                    found = self.find_image_and_tap(template_path)
                    if not found:
                        print(f"⚠️ Không tìm thấy {template_name}, tiếp tục với các bước tiếp theo")
                else:
                    print(f"ℹ️ File {template_name} không tồn tại, bỏ qua bước này")

                # Đợi một chút sau mỗi bước với UI update
                self._random_sleep(2.0, 3.0, f"Chờ sau khi thực hiện {step_description}")
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

            # Bước 9: Tìm và tap vào new_credit_wallet.png (riêng biệt để điều chỉnh timing)
            # Setup hoàn thành, bắt đầu vòng lặp thêm thẻ

            # Vòng lặp thêm thẻ dựa trên num_cards
            print(f"\n🔄 === Bắt đầu vòng lặp thêm {num_cards} thẻ ===")
            successful_cards = 0

            # Cập nhật tổng số thẻ
            self.total_cards = num_cards

            for card_index in range(num_cards):
                print(f"\n🔄 === Bắt đầu thêm thẻ {card_index + 1}/{num_cards} ===")

                # Cập nhật thẻ hiện tại
                self.current_card = card_index + 1

                # Cập nhật trạng thái theo thời gian thực
                self.update_status_realtime()

                # Kiểm tra cờ dừng với debug logging
                if stop_flag_func and stop_flag_func():
                    print(f"🛑 STOP SIGNAL RECEIVED tại thẻ {card_index + 1}/{num_cards}")
                    print(f"🛑 Đang dừng quá trình addwallet...")
                    break

                # Thực hiện quy trình thêm thẻ
                card_success = self.add_single_card(stop_flag_func)

                if card_success:
                    successful_cards += 1
                    print(f"✅ Thành công thêm thẻ {card_index + 1}/{num_cards}")
                else:
                    print(f"❌ Thất bại thêm thẻ {card_index + 1}/{num_cards}")

                # Cập nhật trạng thái sau khi hoàn thành thẻ
                self.update_status_realtime()

                # Nếu không phải thẻ cuối cùng, chuẩn bị cho thẻ tiếp theo
                if card_index < num_cards - 1:
                    print(f"🔄 Chuẩn bị thêm thẻ tiếp theo...")
                    # Quay lại bước credit_wallet để thêm thẻ tiếp theo
                    self._random_sleep(2.0, 3.0, "Chờ trước khi thêm thẻ tiếp theo")

            # Bước cuối: Về màn hình chính
            print("\nBước cuối: Về màn hình chính")
            self.press_key(3)  # KEYCODE_HOME = 3
            self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính")

            print(f"\n=== Hoàn thành: Đã thêm {successful_cards}/{num_cards} thẻ thành công trên thiết bị {self.serial} ===\n")
            return successful_cards > 0

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False
