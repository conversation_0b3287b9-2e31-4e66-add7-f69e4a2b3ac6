import os
import sys
import ctypes
import tempfile

# Biến toàn cục để lưu đường dẫn cơ sở
_BASE_PATH = None
_USER_DATA_PATH = None

def resource_path(relative_path):
    """
    Tr<PERSON> về đường dẫn đầy đủ đến file tà<PERSON> nguyên, phù hợp với cả chế độ chạy script và khi đã đóng gói bằng PyInstaller.
    """
    try:
        # PyInstaller sẽ đặt thuộc tính _MEIPASS vào sys
        base_path = sys._MEIPASS
        print(f"✅ Đang chạy từ PyInstaller với _MEIPASS: {base_path}")
    except AttributeError:
        # Khi chạy dạng script bình thường
        base_path = os.path.abspath(".")
        print(f"ℹ️ Đang chạy dạng script bình thường với base_path: {base_path}")

    full_path = os.path.join(base_path, relative_path)
    print(f"🔍 resource_path: {relative_path} -> {full_path}")
    return full_path

def is_admin():
    """
    Kiểm tra xem ứng dụng có đang chạy với quyền admin không
    """
    try:
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except:
        return False

def get_user_data_path():
    """
    Lấy đường dẫn đến thư mục dữ liệu người dùng, đảm bảo có quyền ghi
    """
    global _USER_DATA_PATH

    if _USER_DATA_PATH is not None:
        return _USER_DATA_PATH

    # Thử sử dụng thư mục AppData
    try:
        appdata = os.environ.get('APPDATA')
        if appdata:
            user_data_dir = os.path.join(appdata, "AutoK")
            if not os.path.exists(user_data_dir):
                os.makedirs(user_data_dir)

            # Kiểm tra quyền ghi
            test_file = os.path.join(user_data_dir, "test_write.tmp")
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                _USER_DATA_PATH = user_data_dir
                print(f"✅ Sử dụng thư mục dữ liệu người dùng: {_USER_DATA_PATH}")
                return _USER_DATA_PATH
            except:
                print("⚠️ Không thể ghi vào thư mục AppData")
    except Exception as e:
        print(f"⚠️ Lỗi khi tạo thư mục trong AppData: {str(e)}")

    # Nếu không thể sử dụng AppData, thử sử dụng thư mục temp
    try:
        temp_dir = os.path.join(tempfile.gettempdir(), "AutoK")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # Kiểm tra quyền ghi
        test_file = os.path.join(temp_dir, "test_write.tmp")
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            _USER_DATA_PATH = temp_dir
            print(f"✅ Sử dụng thư mục temp: {_USER_DATA_PATH}")
            return _USER_DATA_PATH
        except:
            print("⚠️ Không thể ghi vào thư mục temp")
    except Exception as e:
        print(f"⚠️ Lỗi khi tạo thư mục trong temp: {str(e)}")

    # Nếu không thể sử dụng cả AppData và temp, trả về thư mục hiện tại
    _USER_DATA_PATH = os.getcwd()
    print(f"⚠️ Không thể tạo thư mục dữ liệu người dùng, sử dụng thư mục hiện tại: {_USER_DATA_PATH}")
    return _USER_DATA_PATH

def get_base_path():
    """
    Lấy đường dẫn cơ sở của ứng dụng, hoạt động cả khi chạy từ script Python và từ file exe
    """
    global _BASE_PATH

    # Nếu đã thiết lập _BASE_PATH, trả về giá trị đó
    if _BASE_PATH is not None:
        return _BASE_PATH

    # Thử sử dụng sys._MEIPASS (PyInstaller)
    try:
        _BASE_PATH = sys._MEIPASS
        print(f"✅ Đã tự động thiết lập BASE_PATH từ sys._MEIPASS: {_BASE_PATH}")
        return _BASE_PATH
    except AttributeError:
        # Nếu không có sys._MEIPASS, tiếp tục với cách xác định thông thường
        pass

    # Nếu chưa thiết lập, xác định đường dẫn cơ sở
    if getattr(sys, 'frozen', False):
        # Nếu đang chạy từ file exe (đã đóng gói)
        _BASE_PATH = os.path.dirname(sys.executable)
        print(f"✅ Đã tự động thiết lập BASE_PATH từ sys.executable: {_BASE_PATH}")

        # Kiểm tra xem thư mục data có tồn tại không
        data_dir = os.path.join(_BASE_PATH, "data")
        if not os.path.exists(data_dir):
            try:
                os.makedirs(data_dir, exist_ok=True)
                print(f"✅ Đã tạo thư mục data tại: {data_dir}")
            except Exception as e:
                print(f"⚠️ Không thể tạo thư mục data: {str(e)}")
    else:
        # Nếu đang chạy từ script Python
        _BASE_PATH = os.path.dirname(os.path.abspath(__file__))
        print(f"✅ Đã tự động thiết lập BASE_PATH từ __file__: {_BASE_PATH}")

    return _BASE_PATH

def set_base_path(path):
    """
    Thiết lập đường dẫn cơ sở
    """
    global _BASE_PATH
    _BASE_PATH = path
    print(f"✅ Đã thiết lập BASE_PATH: {_BASE_PATH}")

def get_template_path(filename=None):
    """
    Đơn giản hóa: Chỉ tìm ảnh template trong thư mục template ngang với file chính
    - Khi chạy script: tìm trong ./template/
    - Khi đóng gói: tìm trong thư mục ngang với exe/template/
    """
    is_frozen = getattr(sys, 'frozen', False)

    print(f"🔍 get_template_path: filename = {filename}")
    print(f"🔍 get_template_path: is_frozen = {is_frozen}")

    # Xác định thư mục template duy nhất
    if is_frozen:
        # Khi đóng gói: thư mục template ngang với file exe
        exe_dir = os.path.dirname(sys.executable)
        template_dir = os.path.join(exe_dir, "template")
        print(f"🔍 get_template_path: exe_dir = {exe_dir}")
    else:
        # Khi chạy script: thư mục template ngang với file .py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        template_dir = os.path.join(script_dir, "template")
        print(f"🔍 get_template_path: script_dir = {script_dir}")

    print(f"🔍 get_template_path: template_dir = {template_dir}")

    # Tạo thư mục template nếu chưa tồn tại
    if not os.path.exists(template_dir):
        try:
            os.makedirs(template_dir, exist_ok=True)
            print(f"✅ Đã tạo thư mục template tại: {template_dir}")
        except Exception as e:
            print(f"❌ Lỗi khi tạo thư mục template: {str(e)}")
            return None

    if filename:
        file_path = os.path.join(template_dir, filename)
        print(f"🔍 Kiểm tra file template: {file_path}")
        if os.path.exists(file_path):
            print(f"✅ Đã tìm thấy file template {filename} tại: {file_path}")
        else:
            print(f"⚠️ File template {filename} không tồn tại tại: {file_path}")
        return file_path

    print(f"✅ Trả về thư mục template: {template_dir}")
    return template_dir

def get_icon_path(filename=None):
    """
    Lấy đường dẫn đến thư mục icon hoặc file icon cụ thể
    """
    base_path = get_base_path()
    user_data_path = get_user_data_path()
    is_frozen = getattr(sys, 'frozen', False)

    # Danh sách các vị trí có thể chứa thư mục icon
    possible_paths = []

    # Ưu tiên thư mục bên cạnh file exe nếu đang chạy từ exe
    if is_frozen:
        exe_dir = os.path.dirname(sys.executable)
        possible_paths.append(os.path.join(exe_dir, "icon"))

    # Thêm các đường dẫn thông thường
    possible_paths.extend([
        # Trong thư mục gốc
        os.path.join(base_path, "icon"),
        # Trong thư mục _internal khi đóng gói
        os.path.join(base_path, "_internal", "icon"),
        # Trong thư mục dữ liệu người dùng
        os.path.join(user_data_path, "icon")
    ])

    # Kiểm tra từng đường dẫn
    icon_dir = None
    for path in possible_paths:
        if os.path.exists(path):
            icon_dir = path
            break

    # Nếu không tìm thấy, tạo thư mục icon
    if icon_dir is None:
        # Ưu tiên tạo thư mục bên cạnh file exe nếu đang chạy từ exe
        if is_frozen:
            icon_dir = os.path.join(os.path.dirname(sys.executable), "icon")
        else:
            icon_dir = os.path.join(user_data_path, "icon")

        try:
            os.makedirs(icon_dir, exist_ok=True)
        except Exception:
            # Nếu không thể tạo thư mục, sử dụng thư mục hiện tại
            icon_dir = os.getcwd()

    if filename:
        return os.path.join(icon_dir, filename)
    return icon_dir

def get_screen_path(filename=None):
    """
    Đơn giản hóa: Chỉ tìm ảnh screen trong thư mục screen ngang với file chính
    - Khi chạy script: tìm trong ./screen/
    - Khi đóng gói: tìm trong thư mục ngang với exe/screen/
    """
    is_frozen = getattr(sys, 'frozen', False)

    print(f"🔍 get_screen_path: filename = {filename}")
    print(f"🔍 get_screen_path: is_frozen = {is_frozen}")

    # Xác định thư mục screen duy nhất
    if is_frozen:
        # Khi đóng gói: thư mục screen ngang với file exe
        exe_dir = os.path.dirname(sys.executable)
        screen_dir = os.path.join(exe_dir, "screen")
        print(f"🔍 get_screen_path: exe_dir = {exe_dir}")
    else:
        # Khi chạy script: thư mục screen ngang với file .py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        screen_dir = os.path.join(script_dir, "screen")
        print(f"🔍 get_screen_path: script_dir = {script_dir}")

    print(f"🔍 get_screen_path: screen_dir = {screen_dir}")

    # Tạo thư mục screen nếu chưa tồn tại
    if not os.path.exists(screen_dir):
        try:
            os.makedirs(screen_dir, exist_ok=True)
            print(f"✅ Đã tạo thư mục screen tại: {screen_dir}")
        except Exception as e:
            print(f"❌ Lỗi khi tạo thư mục screen: {str(e)}")
            return None

    if filename:
        file_path = os.path.join(screen_dir, filename)
        print(f"🔍 Kiểm tra file screen: {file_path}")
        if os.path.exists(file_path):
            print(f"✅ Đã tìm thấy file screen {filename} tại: {file_path}")
        else:
            print(f"⚠️ File screen {filename} không tồn tại tại: {file_path}")
        return file_path

    print(f"✅ Trả về thư mục screen: {screen_dir}")
    return screen_dir

def get_txt_path(filename):
    """
    Đơn giản hóa: Chỉ tìm file txt trong thư mục data
    - Khi chạy script: tìm trong ./data/
    - Khi đóng gói: tìm trong thư mục ngang với exe/data/
    """
    is_frozen = getattr(sys, 'frozen', False)

    print(f"🔍 get_txt_path: filename = {filename}")
    print(f"🔍 get_txt_path: is_frozen = {is_frozen}")

    # Xác định thư mục data duy nhất
    if is_frozen:
        # Khi đóng gói: thư mục data ngang với file exe
        exe_dir = os.path.dirname(sys.executable)
        data_dir = os.path.join(exe_dir, "data")
        print(f"🔍 get_txt_path: exe_dir = {exe_dir}")
    else:
        # Khi chạy script: thư mục data ngang với file .py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(script_dir, "data")
        print(f"🔍 get_txt_path: script_dir = {script_dir}")

    print(f"🔍 get_txt_path: data_dir = {data_dir}")

    # Tạo thư mục data nếu chưa tồn tại
    if not os.path.exists(data_dir):
        try:
            os.makedirs(data_dir, exist_ok=True)
            print(f"✅ Đã tạo thư mục data tại: {data_dir}")
        except Exception as e:
            print(f"❌ Lỗi khi tạo thư mục data: {str(e)}")
            return None

    # Đường dẫn file
    file_path = os.path.join(data_dir, filename)
    print(f"🔍 Kiểm tra đường dẫn: {file_path}")

    # Kiểm tra file có tồn tại không
    if os.path.exists(file_path):
        print(f"✅ Đã tìm thấy file {filename} tại: {file_path}")
        return file_path

    # Tạo file mẫu nếu là mail.txt hoặc card.txt
    if filename in ["mail.txt", "card.txt"]:
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                if filename == "mail.txt":
                    f.write("# Danh sách email (format: email|password|authentication)\n")
                    f.write("# Ví dụ: <EMAIL>|password123|ABCDEFGHIJKLMNOP\n")
                elif filename == "card.txt":
                    f.write("# Danh sách thẻ (format: số thẻ|tháng|năm|cvv)\n")
                    f.write("# Ví dụ: ****************|01|25|123\n")
            print(f"✅ Đã tạo file {filename} mẫu tại: {file_path}")
        except Exception as e:
            print(f"❌ Lỗi khi tạo file {filename} mẫu: {str(e)}")

    print(f"⚠️ File {filename} không tồn tại, trả về đường dẫn: {file_path}")
    return file_path

def get_data_path(filename=None):
    """
    Đơn giản hóa: Sử dụng get_txt_path cho file data
    """
    print(f"🔍 get_data_path: filename = {filename}")

    if filename:
        # Sử dụng get_txt_path cho file cụ thể
        return get_txt_path(filename)
    else:
        # Trả về thư mục data
        is_frozen = getattr(sys, 'frozen', False)

        if is_frozen:
            # Khi đóng gói: thư mục data ngang với file exe
            exe_dir = os.path.dirname(sys.executable)
            data_dir = os.path.join(exe_dir, "data")
        else:
            # Khi chạy script: thư mục data ngang với file .py
            script_dir = os.path.dirname(os.path.abspath(__file__))
            data_dir = os.path.join(script_dir, "data")

        # Tạo thư mục data nếu chưa tồn tại
        if not os.path.exists(data_dir):
            try:
                os.makedirs(data_dir, exist_ok=True)
                print(f"✅ Đã tạo thư mục data tại: {data_dir}")
            except Exception as e:
                print(f"❌ Lỗi khi tạo thư mục data: {str(e)}")

        print(f"✅ Trả về thư mục data: {data_dir}")
        return data_dir

def get_platform_tools_path(filename=None):
    """
    Lấy đường dẫn đến thư mục platform-tools hoặc file cụ thể trong thư mục đó
    """
    base_path = get_base_path()
    user_data_path = get_user_data_path()
    is_frozen = getattr(sys, 'frozen', False)

    # Danh sách các vị trí có thể chứa platform-tools
    possible_paths = []

    # Ưu tiên thư mục bên cạnh file exe nếu đang chạy từ exe
    if is_frozen:
        exe_dir = os.path.dirname(sys.executable)
        possible_paths.append(os.path.join(exe_dir, "platform-tools"))

    # Thêm các đường dẫn thông thường
    possible_paths.extend([
        # Trong thư mục gốc
        os.path.join(base_path, "platform-tools"),
        # Trong thư mục _internal khi đóng gói
        os.path.join(base_path, "_internal", "platform-tools"),
        # Trong thư mục dữ liệu người dùng
        os.path.join(user_data_path, "platform-tools"),
        # Các vị trí cài đặt phổ biến
        r"C:\Program Files\platform-tools",
        r"C:\platform-tools",
        r"C:\tools\platform-tools",
        # Đường dẫn mặc định (ưu tiên thấp nhất)
        ""
    ])

    # Kiểm tra từng đường dẫn
    for path in possible_paths:
        if path and os.path.exists(path):
            if filename:
                file_path = os.path.join(path, filename)
                if os.path.exists(file_path):
                    print(f"✅ Đã tìm thấy {filename} tại: {file_path}")
                    return file_path
            else:
                print(f"✅ Đã tìm thấy thư mục platform-tools tại: {path}")
                return path

    # Nếu không tìm thấy, thử sao chép từ thư mục _internal vào thư mục bên cạnh file exe hoặc thư mục dữ liệu người dùng
    internal_platform_tools = os.path.join(base_path, "_internal", "platform-tools")
    if os.path.exists(internal_platform_tools):
        # Xác định thư mục đích để sao chép
        if is_frozen:
            target_platform_tools = os.path.join(os.path.dirname(sys.executable), "platform-tools")
        else:
            target_platform_tools = os.path.join(user_data_path, "platform-tools")

        try:
            if not os.path.exists(target_platform_tools):
                os.makedirs(target_platform_tools, exist_ok=True)
                print(f"✅ Đã tạo thư mục platform-tools tại: {target_platform_tools}")

            # Sao chép các file và thư mục từ thư mục _internal
            import shutil

            # Sao chép toàn bộ thư mục platform-tools
            try:
                # Xóa thư mục đích nếu đã tồn tại
                if os.path.exists(target_platform_tools):
                    shutil.rmtree(target_platform_tools)
                    print(f"✅ Đã xóa thư mục platform-tools cũ tại: {target_platform_tools}")

                # Sao chép toàn bộ thư mục
                shutil.copytree(internal_platform_tools, target_platform_tools)
                print(f"✅ Đã sao chép toàn bộ thư mục platform-tools từ {internal_platform_tools} đến {target_platform_tools}")
            except Exception as e:
                print(f"⚠️ Không thể sao chép thư mục platform-tools: {str(e)}")

                # Nếu không thể sao chép toàn bộ thư mục, thử sao chép từng file
                try:
                    # Tạo thư mục đích nếu chưa tồn tại
                    if not os.path.exists(target_platform_tools):
                        os.makedirs(target_platform_tools, exist_ok=True)

                    # Sao chép từng file
                    for item in os.listdir(internal_platform_tools):
                        src_item = os.path.join(internal_platform_tools, item)
                        dst_item = os.path.join(target_platform_tools, item)

                        if os.path.isfile(src_item):
                            try:
                                shutil.copy2(src_item, dst_item)
                                print(f"✅ Đã sao chép {item} từ {internal_platform_tools} đến {target_platform_tools}")
                            except Exception as e:
                                print(f"⚠️ Không thể sao chép {item}: {str(e)}")
                        elif os.path.isdir(src_item):
                            try:
                                if os.path.exists(dst_item):
                                    shutil.rmtree(dst_item)
                                shutil.copytree(src_item, dst_item)
                                print(f"✅ Đã sao chép thư mục {item} từ {internal_platform_tools} đến {target_platform_tools}")
                            except Exception as e:
                                print(f"⚠️ Không thể sao chép thư mục {item}: {str(e)}")
                except Exception as e:
                    print(f"⚠️ Không thể sao chép các file platform-tools: {str(e)}")

            # Kiểm tra lại sau khi sao chép
            if filename:
                file_path = os.path.join(target_platform_tools, filename)
                if os.path.exists(file_path):
                    print(f"✅ Đã tìm thấy {filename} tại: {file_path} sau khi sao chép")
                    return file_path
            else:
                print(f"✅ Đã tìm thấy thư mục platform-tools tại: {target_platform_tools} sau khi sao chép")
                return target_platform_tools
        except Exception as e:
            print(f"⚠️ Không thể sao chép thư mục platform-tools: {str(e)}")

    # Nếu vẫn không tìm thấy, trả về None
    print(f"⚠️ Không tìm thấy {filename if filename else 'thư mục platform-tools'}")
    return None

def get_scrcpy_path(filename=None):
    """
    Lấy đường dẫn đến thư mục scrcpy hoặc file cụ thể trong thư mục đó
    """
    base_path = get_base_path()
    user_data_path = get_user_data_path()
    is_frozen = getattr(sys, 'frozen', False)

    # Danh sách các vị trí có thể chứa scrcpy
    possible_paths = []

    # Ưu tiên thư mục bên cạnh file exe nếu đang chạy từ exe
    if is_frozen:
        exe_dir = os.path.dirname(sys.executable)
        possible_paths.append(os.path.join(exe_dir, "scrcpy"))

    # Thêm các đường dẫn thông thường
    possible_paths.extend([
        # Trong thư mục gốc
        os.path.join(base_path, "scrcpy"),
        # Trong thư mục _internal khi đóng gói
        os.path.join(base_path, "_internal", "scrcpy"),
        # Trong thư mục dữ liệu người dùng
        os.path.join(user_data_path, "scrcpy"),
        # Các vị trí cài đặt phổ biến
        r"C:\Program Files\scrcpy",
        r"C:\scrcpy",
        r"C:\tools\scrcpy",
        # Đường dẫn mặc định (ưu tiên thấp nhất)
        ""
    ])

    # Kiểm tra từng đường dẫn
    for path in possible_paths:
        if path and os.path.exists(path):
            if filename:
                # Tìm kiếm file trong thư mục gốc
                file_path = os.path.join(path, filename)
                if os.path.exists(file_path):
                    print(f"✅ Đã tìm thấy {filename} tại: {file_path}")
                    return file_path

                # Tìm kiếm file trong các thư mục con
                for root, _, files in os.walk(path):
                    if filename in files:
                        file_path = os.path.join(root, filename)
                        print(f"✅ Đã tìm thấy {filename} tại: {file_path} (trong thư mục con)")
                        return file_path
            else:
                print(f"✅ Đã tìm thấy thư mục scrcpy tại: {path}")
                return path

    # Nếu không tìm thấy, thử sao chép từ thư mục _internal vào thư mục bên cạnh file exe hoặc thư mục dữ liệu người dùng
    internal_scrcpy = os.path.join(base_path, "_internal", "scrcpy")
    if os.path.exists(internal_scrcpy):
        # Xác định thư mục đích để sao chép
        if is_frozen:
            target_scrcpy = os.path.join(os.path.dirname(sys.executable), "scrcpy")
        else:
            target_scrcpy = os.path.join(user_data_path, "scrcpy")

        try:
            if not os.path.exists(target_scrcpy):
                os.makedirs(target_scrcpy, exist_ok=True)
                print(f"✅ Đã tạo thư mục scrcpy tại: {target_scrcpy}")

            # Sao chép các file và thư mục từ thư mục _internal
            import shutil

            # Sao chép toàn bộ thư mục scrcpy
            try:
                # Xóa thư mục đích nếu đã tồn tại
                if os.path.exists(target_scrcpy):
                    shutil.rmtree(target_scrcpy)
                    print(f"✅ Đã xóa thư mục scrcpy cũ tại: {target_scrcpy}")

                # Sao chép toàn bộ thư mục
                shutil.copytree(internal_scrcpy, target_scrcpy)
                print(f"✅ Đã sao chép toàn bộ thư mục scrcpy từ {internal_scrcpy} đến {target_scrcpy}")
            except Exception as e:
                print(f"⚠️ Không thể sao chép thư mục scrcpy: {str(e)}")

                # Nếu không thể sao chép toàn bộ thư mục, thử sao chép từng file
                try:
                    # Tạo thư mục đích nếu chưa tồn tại
                    if not os.path.exists(target_scrcpy):
                        os.makedirs(target_scrcpy, exist_ok=True)

                    # Sao chép từng file
                    for item in os.listdir(internal_scrcpy):
                        src_item = os.path.join(internal_scrcpy, item)
                        dst_item = os.path.join(target_scrcpy, item)

                        if os.path.isfile(src_item):
                            try:
                                shutil.copy2(src_item, dst_item)
                                print(f"✅ Đã sao chép {item} từ {internal_scrcpy} đến {target_scrcpy}")
                            except Exception as e:
                                print(f"⚠️ Không thể sao chép {item}: {str(e)}")
                        elif os.path.isdir(src_item):
                            try:
                                if os.path.exists(dst_item):
                                    shutil.rmtree(dst_item)
                                shutil.copytree(src_item, dst_item)
                                print(f"✅ Đã sao chép thư mục {item} từ {internal_scrcpy} đến {target_scrcpy}")
                            except Exception as e:
                                print(f"⚠️ Không thể sao chép thư mục {item}: {str(e)}")
                except Exception as e:
                    print(f"⚠️ Không thể sao chép các file scrcpy: {str(e)}")

            # Kiểm tra lại sau khi sao chép
            if filename:
                file_path = os.path.join(target_scrcpy, filename)
                if os.path.exists(file_path):
                    print(f"✅ Đã tìm thấy {filename} tại: {file_path} sau khi sao chép")
                    return file_path
            else:
                print(f"✅ Đã tìm thấy thư mục scrcpy tại: {target_scrcpy} sau khi sao chép")
                return target_scrcpy
        except Exception as e:
            print(f"⚠️ Không thể sao chép thư mục scrcpy: {str(e)}")

    # Nếu vẫn không tìm thấy, trả về None
    print(f"⚠️ Không tìm thấy {filename if filename else 'thư mục scrcpy'}")
    return None
