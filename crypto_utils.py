"""
Công cụ mã hóa và giải mã cho hệ thống quản lý license key
"""

import os
import tempfile
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import base64

# Mật khẩu mặc định để mã hóa/giải mã file credentials
DEFAULT_PASSWORD = "AutoKhuong2024"

def generate_key(password, salt, iterations=100000):
    """
    Tạo key từ password và salt sử dụng PBKDF2
    
    :param password: Mật khẩu
    :param salt: Salt (bytes)
    :param iterations: Số vòng lặp
    :return: Key (bytes)
    """
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,  # 32 bytes = 256 bits
        salt=salt,
        iterations=iterations,
    )
    return kdf.derive(password.encode())

def encrypt_file(input_file, output_file, password, salt=None):
    """
    Mã hóa file
    
    :param input_file: Đường dẫn file đầu vào
    :param output_file: Đường dẫn file đầu ra
    :param password: Mật khẩu
    :param salt: Salt (bytes), nếu None sẽ tạo ngẫu nhiên
    :return: Salt đã sử dụng
    """
    # Tạo salt nếu không được cung cấp
    if salt is None:
        salt = os.urandom(16)
    
    # Tạo key từ password và salt
    key = generate_key(password, salt)
    
    # Tạo IV ngẫu nhiên
    iv = os.urandom(16)
    
    # Đọc nội dung file đầu vào
    with open(input_file, 'rb') as f:
        plaintext = f.read()
    
    # Mã hóa
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    encryptor = cipher.encryptor()
    
    # Padding
    padding_length = 16 - (len(plaintext) % 16)
    padded_plaintext = plaintext + bytes([padding_length]) * padding_length
    
    # Mã hóa dữ liệu
    ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()
    
    # Ghi IV và ciphertext vào file đầu ra
    with open(output_file, 'wb') as f:
        f.write(iv + ciphertext)
    
    return salt

def decrypt_file(input_file, output_file, password, salt):
    """
    Giải mã file
    
    :param input_file: Đường dẫn file đầu vào (đã mã hóa)
    :param output_file: Đường dẫn file đầu ra (giải mã)
    :param password: Mật khẩu
    :param salt: Salt (bytes)
    :return: True nếu giải mã thành công, False nếu không
    """
    try:
        # Tạo key từ password và salt
        key = generate_key(password, salt)
        
        # Đọc nội dung file đầu vào
        with open(input_file, 'rb') as f:
            data = f.read()
        
        # Tách IV và ciphertext
        iv = data[:16]
        ciphertext = data[16:]
        
        # Giải mã
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        
        # Xóa padding
        padding_length = padded_plaintext[-1]
        plaintext = padded_plaintext[:-padding_length]
        
        # Ghi dữ liệu giải mã vào file đầu ra
        with open(output_file, 'wb') as f:
            f.write(plaintext)
        
        return True
    except Exception as e:
        print(f"❌ Lỗi khi giải mã file: {str(e)}")
        return False

def get_temp_credentials_file(encrypted_file, password, salt):
    """
    Giải mã file credentials và lưu vào file tạm thời
    
    :param encrypted_file: Đường dẫn file credentials đã mã hóa
    :param password: Mật khẩu
    :param salt: Salt (bytes)
    :return: Đường dẫn đến file tạm thời chứa credentials đã giải mã
    """
    # Tạo file tạm thời
    fd, temp_file = tempfile.mkstemp(suffix='.json')
    os.close(fd)
    
    # Giải mã file
    success = decrypt_file(encrypted_file, temp_file, password, salt)
    
    if not success:
        raise Exception("Không thể giải mã file credentials")
    
    return temp_file

def encrypt_credentials(input_file, output_file, salt_file, password=DEFAULT_PASSWORD):
    """
    Mã hóa file credentials và lưu salt
    
    :param input_file: Đường dẫn file credentials gốc (JSON)
    :param output_file: Đường dẫn file đầu ra (đã mã hóa)
    :param salt_file: Đường dẫn file để lưu salt
    :param password: Mật khẩu, mặc định là DEFAULT_PASSWORD
    :return: True nếu mã hóa thành công, False nếu không
    """
    try:
        # Tạo salt ngẫu nhiên
        salt = os.urandom(16)
        
        # Mã hóa file
        encrypt_file(input_file, output_file, password, salt)
        
        # Lưu salt vào file
        with open(salt_file, 'wb') as f:
            f.write(salt)
        
        print(f"✅ Đã mã hóa file credentials thành công: {output_file}")
        print(f"✅ Đã lưu salt vào file: {salt_file}")
        
        return True
    except Exception as e:
        print(f"❌ Lỗi khi mã hóa file credentials: {str(e)}")
        return False

if __name__ == "__main__":
    # Ví dụ sử dụng
    import sys
    
    if len(sys.argv) < 3:
        print("Sử dụng: python crypto_utils.py encrypt|decrypt <input_file> <output_file> [password]")
        sys.exit(1)
    
    action = sys.argv[1].lower()
    input_file = sys.argv[2]
    output_file = sys.argv[3]
    password = sys.argv[4] if len(sys.argv) > 4 else DEFAULT_PASSWORD
    
    if action == "encrypt":
        salt_file = output_file + ".salt"
        if encrypt_credentials(input_file, output_file, salt_file, password):
            print(f"✅ Mã hóa thành công: {input_file} -> {output_file}")
            print(f"✅ Salt đã được lưu vào: {salt_file}")
        else:
            print("❌ Mã hóa thất bại")
    elif action == "decrypt":
        salt_file = input_file + ".salt"
        if os.path.exists(salt_file):
            with open(salt_file, 'rb') as f:
                salt = f.read()
            if decrypt_file(input_file, output_file, password, salt):
                print(f"✅ Giải mã thành công: {input_file} -> {output_file}")
            else:
                print("❌ Giải mã thất bại")
        else:
            print(f"❌ Không tìm thấy file salt: {salt_file}")
    else:
        print("❌ Hành động không hợp lệ. Sử dụng 'encrypt' hoặc 'decrypt'")
