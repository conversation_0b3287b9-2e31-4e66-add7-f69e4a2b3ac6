import sys
import subprocess
import random
import time
import os
import cv2
import numpy as np
import datetime
from PyQt5 import QtWidgets

# Import path_manager để quản lý đường dẫn
import path_manager

# Import để phát âm thanh
try:
    import winsound
    WINSOUND_AVAILABLE = True
except ImportError:
    WINSOUND_AVAILABLE = False

# Biến toàn cục để lưu đường dẫn ADB
ADB_PATH = "adb"  # Giá trị mặc định

class mua_game:
    """
    Thực hiện các hành động mô phỏng thao tác người dùng trên thiết bị Android qua ADB.
    """

    def __init__(self, serial, device_index=0, total_devices=1, adb_path=None, password=None, goi1_value=None, goi2_value=None, goi3_value=None):
        self.serial = serial
        # Biến đếm số lần lặp hiện tại (không cần num_loops nữa)
        # self.main_app = main_app  # Tham chiếu đến MainApp (đã loại bỏ vì chưa định nghĩa main_app)
        self.loop_count = 0
        # Biến để theo dõi gói hiện tại (1, 2, 3)
        self.current_package = 0
        # Biến để theo dõi số lần mua gói hiện tại
        self.package_count = 0
        # Danh sách vị trí đã tap để tránh tap trùng
        self.tapped_positions = []
        # Biến để theo dõi số thẻ đã tap
        self.card_count = 0
        # Biến để theo dõi thứ tự thẻ hiện tại (bắt đầu từ thẻ thứ 2)
        self.current_card_number = 0
        # Kích thước màn hình mặc định
        self.screen_width = 1080
        self.screen_height = 1920
        # Chỉ số thiết bị (sử dụng trong load_password_from_mail)
        self.device_index = device_index
        # Tổng số thiết bị
        self.total_devices = total_devices
        # Mật khẩu từ giao diện (ưu tiên cao nhất)
        self.password = password
        # Giá trị số lần mua cho từng gói
        self.goi1_value = goi1_value if goi1_value is not None else 5
        self.goi2_value = goi2_value if goi2_value is not None else 5
        self.goi3_value = goi3_value if goi3_value is not None else 30

        # In thông tin debug
        print(f"🔍 mua.__init__: serial = {serial}")
        print(f"🔍 mua.__init__: device_index = {device_index}")
        print(f"🔍 mua.__init__: total_devices = {total_devices}")
        print(f"🔍 mua.__init__: adb_path = {adb_path}")
        print(f"🔍 mua.__init__: sys.executable = {sys.executable}")
        print(f"🔍 mua.__init__: os.getcwd() = {os.getcwd()}")
        print(f"🔍 mua.__init__: __file__ = {__file__}")
        print(f"🔍 mua.__init__: goi1_value = {self.goi1_value}")
        print(f"🔍 mua.__init__: goi2_value = {self.goi2_value}")
        print(f"🔍 mua.__init__: goi3_value = {self.goi3_value}")

        # Kiểm tra mật khẩu từ giao diện
        if self.password:
            masked_password = self.password[:2] + '*' * (len(self.password) - 2) if len(self.password) > 2 else '*' * len(self.password)
            print(f"✅ Đã nhận mật khẩu từ giao diện: {masked_password}")
        else:
            print("ℹ️ Không có mật khẩu từ giao diện, sẽ tải từ file mail.txt")

        # Kiểm tra xem có đang chạy từ file exe không
        is_frozen = getattr(sys, 'frozen', False)
        print(f"🔍 mua.__init__: is_frozen = {is_frozen}")

        # Lấy đường dẫn đến thư mục template
        template_dir = path_manager.get_template_path()
        self.game1_template_path = os.path.join(template_dir, "game1.png")
        self.nap_goi_template_path = os.path.join(template_dir, "nap_goi.png")
        self.ok_template_path = os.path.join(template_dir, "ok.png")
        self.success_template_path = os.path.join(template_dir, "success.png")
        self.got_it_template_path = os.path.join(template_dir, "got_it.png")
        self.tap_join_template_path = os.path.join(template_dir, "tap_join.png")
        self.agree_template_path = os.path.join(template_dir, "agree.png")

        # Thêm template paths cho các gói
        self.goi1_template_path = os.path.join(template_dir, "goi1.png")
        self.goi2_template_path = os.path.join(template_dir, "goi2.png")
        self.goi3_template_path = os.path.join(template_dir, "goi3.png")

        # Cập nhật biến toàn cục ADB_PATH nếu được cung cấp
        global ADB_PATH
        if adb_path:
            ADB_PATH = adb_path
            print(f"✅ Đã cập nhật đường dẫn ADB: {ADB_PATH}")

        # Lấy kích thước màn hình thực tế của thiết bị
        self.get_device_screen_size()

        # Tải mật khẩu từ file mail.txt nếu không có mật khẩu từ giao diện
        if not self.password:
            self.load_password_from_mail()

    def play_sound(self, sound_file):
        """
        Phát file âm thanh meo.wav khi vào vòng lặp gói
        """
        try:
            if not WINSOUND_AVAILABLE:
                return False

            # Lấy đường dẫn đến thư mục icon
            icon_dir = path_manager.get_icon_path()
            sound_path = os.path.join(icon_dir, sound_file)

            # Kiểm tra file tồn tại
            if not os.path.exists(sound_path):
                return False

            # Phát âm thanh bằng winsound
            winsound.PlaySound(sound_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
            return True

        except Exception:
            return False

    def load_password_from_mail(self):
        """
        Đọc mật khẩu từ file mail.txt dựa trên chỉ số thiết bị
        Format của file: email|password|authentication
        """
        try:
            # Danh sách các vị trí có thể chứa file mail.txt
            possible_paths = []

            # Kiểm tra xem có đang chạy từ file exe không
            is_frozen = getattr(sys, 'frozen', False)
            print(f"🔍 load_password_from_mail: is_frozen = {is_frozen}")

            # Thêm đường dẫn đặc biệt cho file exe (ưu tiên cao nhất)
            if is_frozen:
                # Thêm đường dẫn tương đối với thư mục chứa file exe
                exe_dir = os.path.dirname(sys.executable)
                possible_paths.append(os.path.join(exe_dir, "data", "mail.txt"))
                possible_paths.append(os.path.join(exe_dir, "mail.txt"))

            # Thêm các đường dẫn thông thường
            base_path = os.path.abspath(".")
            possible_paths.extend([
                # Trong thư mục hiện tại
                os.path.join(base_path, "mail.txt"),
                # Trong thư mục data
                os.path.join(base_path, "data", "mail.txt"),
                # Trong thư mục cha
                os.path.join(os.path.dirname(base_path), "mail.txt"),
                # Trong thư mục data của thư mục cha
                os.path.join(os.path.dirname(base_path), "data", "mail.txt"),
            ])

            # Thử sử dụng path_manager
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                mail_file_path = path_manager.get_txt_path("mail.txt")
                possible_paths.append(mail_file_path)
                print(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")

            try:
                # Sử dụng get_data_path như phương án dự phòng
                mail_file_path = path_manager.get_data_path("mail.txt")
                possible_paths.append(mail_file_path)
                print(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_data_path: {str(e)}")

            # In thông tin debug
            print(f"🔍 Danh sách các đường dẫn có thể chứa mail.txt:")
            for idx, path in enumerate(possible_paths):
                print(f"  {idx+1}. {path}")

            # Tìm file mail.txt trong danh sách đường dẫn
            mail_file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mail_file_path = path
                    print(f"✅ Đã tìm thấy file mail.txt tại: {mail_file_path}")
                    break

            # Nếu không tìm thấy, tạo file mail.txt mẫu
            if mail_file_path is None:
                print(f"⚠️ Không tìm thấy file mail.txt trong tất cả các đường dẫn")
                self.update_status(f"⚠️ Không tìm thấy file mail.txt")

                # Chọn đường dẫn để tạo file mail.txt mẫu
                if is_frozen:
                    # Nếu đang chạy từ file exe, tạo file trong thư mục data bên cạnh file exe
                    exe_dir = os.path.dirname(sys.executable)
                    data_dir = os.path.join(exe_dir, "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    mail_file_path = os.path.join(data_dir, "mail.txt")
                else:
                    # Nếu đang chạy từ script, tạo file trong thư mục data
                    data_dir = os.path.join(base_path, "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    mail_file_path = os.path.join(data_dir, "mail.txt")

                # Tạo file mail.txt mẫu
                try:
                    with open(mail_file_path, "w") as f:
                        f.write("# Danh sách email (format: email|password|authentication)\n")
                        f.write("# Ví dụ: <EMAIL>|password123|ABCDEFGHIJKLMNOP\n")
                    print(f"✅ Đã tạo file mail.txt mẫu tại: {mail_file_path}")
                    self.update_status(f"✅ Đã tạo file mail.txt mẫu")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file mail.txt mẫu: {str(e)}")
                return

            # Kiểm tra thư mục cha
            parent_dir = os.path.dirname(mail_file_path)
            print(f"🔍 Thư mục cha: {parent_dir}")
            if os.path.exists(parent_dir):
                print(f"✅ Thư mục cha tồn tại: {parent_dir}")
                print(f"🔍 Nội dung thư mục cha:")
                try:
                    for item in os.listdir(parent_dir):
                        print(f"  - {item}")
                except Exception as e:
                    print(f"❌ Lỗi khi liệt kê nội dung thư mục cha: {str(e)}")
            else:
                print(f"❌ Thư mục cha không tồn tại: {parent_dir}")
                try:
                    os.makedirs(parent_dir, exist_ok=True)
                    print(f"✅ Đã tạo thư mục cha: {parent_dir}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo thư mục cha: {str(e)}")

            print(f"✅ File mail.txt tồn tại: {mail_file_path}")

            # Đọc danh sách email từ file
            try:
                with open(mail_file_path, "r", encoding="utf-8") as file:
                    lines = file.readlines()
                print(f"✅ Đã đọc {len(lines)} dòng từ file {mail_file_path}")
            except Exception as e:
                print(f"❌ Lỗi khi đọc file {mail_file_path}: {str(e)}")
                # Thử đọc với encoding khác
                try:
                    with open(mail_file_path, "r") as file:
                        lines = file.readlines()
                    print(f"✅ Đã đọc {len(lines)} dòng từ file {mail_file_path} (không có encoding)")
                except Exception as e2:
                    print(f"❌ Lỗi khi đọc file {mail_file_path} (không có encoding): {str(e2)}")
                    self.update_status(f"❌ Không thể đọc file mail.txt")
                    return

            # In nội dung file để debug (ẩn mật khẩu)
            print(f"🔍 Nội dung file mail.txt:")
            for i, line in enumerate(lines):
                # Ẩn mật khẩu trong dòng
                if "|" in line:
                    parts = line.split("|")
                    if len(parts) >= 2:
                        masked_line = parts[0] + "|" + "*" * len(parts[1])
                        if len(parts) > 2:
                            masked_line += "|" + "|".join(parts[2:])
                        print(f"  {i+1}. {masked_line}")
                    else:
                        print(f"  {i+1}. {line.strip()}")
                else:
                    print(f"  {i+1}. {line.strip()}")

            # Lọc các dòng trống và dòng comment
            valid_lines = []
            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                valid_lines.append(line)

            print(f"✅ Đã lọc được {len(valid_lines)} dòng hợp lệ từ {len(lines)} dòng")

            # Kiểm tra xem có email nào không
            if not valid_lines:
                print(f"⚠️ Không có email nào trong file {mail_file_path}")
                print(f"⚠️ Vui lòng thêm email vào file với định dạng: email|password|authentication")
                self.update_status(f"⚠️ Không có email nào trong file mail.txt")
                return

            # Kiểm tra xem có đủ email cho thiết bị không
            if self.device_index >= len(valid_lines):
                print(f"⚠️ Không đủ email trong file {mail_file_path} cho thiết bị {self.serial} (index {self.device_index})")
                print(f"⚠️ Số email hiện có: {len(valid_lines)}, cần ít nhất {self.device_index + 1} email")
                self.update_status(f"⚠️ Hết mail - Không đủ email trong file mail.txt")

                # Nếu không đủ email, sử dụng email theo chỉ số thiết bị mod số lượng email
                if len(valid_lines) > 0:
                    self.device_index = self.device_index % len(valid_lines)
                    print(f"ℹ️ Sử dụng email ở vị trí {self.device_index + 1} cho thiết bị {self.serial}")
                else:
                    return

            # Lấy thông tin email tương ứng với chỉ số thiết bị
            # Sắp xếp danh sách email theo thứ tự từ trên xuống dưới trong file mail.txt
            email_index = self.device_index
            if email_index >= len(valid_lines):
                email_index = email_index % len(valid_lines)

            email_line = valid_lines[email_index]
            print(f"🔍 Thiết bị {self.serial} (index {self.device_index}) sử dụng email ở vị trí {email_index + 1}: {email_line.split('|')[0]}|***")

            # Phân tích dòng email
            email_info = email_line.split('|')
            print(f"🔍 Số phần tử sau khi phân tích: {len(email_info)}")

            # Kiểm tra định dạng email
            if len(email_info) >= 2:
                # Lấy mật khẩu từ cột thứ 2
                self.password = email_info[1].strip()
                # Hiển thị 2 ký tự đầu của mật khẩu và che phần còn lại
                masked_password = self.password[:2] + '*' * (len(self.password) - 2) if len(self.password) > 2 else '*' * len(self.password)
                print(f"✅ Đã tải mật khẩu cho thiết bị {self.serial} (index {self.device_index}, email vị trí {email_index + 1}): {masked_password}")
                self.update_status(f"✅ Đã tải mật khẩu từ mail.txt (vị trí {email_index + 1})")
            else:
                print(f"⚠️ Định dạng email không hợp lệ: {email_line.split('|')[0]}|***")
                print(f"⚠️ Định dạng cần thiết: email|password|authentication")
                self.update_status(f"⚠️ Định dạng email không hợp lệ")

        except Exception as e:
            print(f"❌ Lỗi khi tải mật khẩu từ file mail.txt: {str(e)}")
            self.update_status(f"❌ Lỗi khi tải mật khẩu: {str(e)}")

    def get_device_screen_size(self):
        """
        Lấy kích thước màn hình thực tế của thiết bị.
        """
        try:
            # Sử dụng dumpsys window để lấy kích thước màn hình
            result = subprocess.run(
                [ADB_PATH, "-s", self.serial, "shell", "dumpsys", "window", "displays"],
                capture_output=True, text=True, check=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            # Tìm kiếm thông tin kích thước màn hình trong output
            output = result.stdout
            # Tìm kiếm các mẫu như "init=1080x1920" hoặc "cur=1080x1920"
            import re
            size_match = re.search(r'init=(\d+)x(\d+)', output) or re.search(r'cur=(\d+)x(\d+)', output)

            if size_match:
                self.screen_width = int(size_match.group(1))
                self.screen_height = int(size_match.group(2))
                print(f"✅ Đã lấy kích thước màn hình: {self.screen_width}x{self.screen_height}")
            else:
                print(f"⚠️ Không thể xác định kích thước màn hình, sử dụng giá trị mặc định: {self.screen_width}x{self.screen_height}")
        except Exception as e:
            print(f"❌ Lỗi khi lấy kích thước màn hình: {str(e)}")
            print(f"⚠️ Sử dụng kích thước màn hình mặc định: {self.screen_width}x{self.screen_height}")

    def tap_at_position(self, x, y, description=""):
        """
        Tap vào vị trí cụ thể trên màn hình.
        :param x: Tọa độ x
        :param y: Tọa độ y
        :param description: Mô tả hành động (để ghi log)
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"Tap vào vị trí ({x}, {y}){' - ' + description if description else ''}")
            subprocess.run(
                [ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(x), str(y)],
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )
            print(f"✅ Đã tap vào vị trí ({x}, {y}){' - ' + description if description else ''}")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi tap vào vị trí ({x}, {y}): {str(e)}")
            return False

    def find_and_tap_package(self, package_template_path, package_name, attempt_count, total_count, check_stop_flag=None):
        """
        Tìm và tap vào gói cụ thể
        :param package_template_path: Đường dẫn đến template của gói
        :param package_name: Tên gói (Gói 1, Gói 2, Gói 3)
        :param attempt_count: Số lần thử hiện tại
        :param total_count: Tổng số lần thử
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Kiểm tra cờ dừng trước khi thực hiện
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm {package_name}")
                    return False

            # Kiểm tra xem file template có tồn tại không
            if not os.path.exists(package_template_path):
                print(f"❌ Không tìm thấy file template {os.path.basename(package_template_path)}")
                return False

            print(f"🎯 Tìm và tap vào {package_name} - Lần {attempt_count}/{total_count}")

            # Thử tìm và tap vào gói
            if self.find_image_and_tap(package_template_path, check_stop_flag=check_stop_flag):
                print(f"✅ Đã tìm thấy và tap vào {package_name}")
                return True
            else:
                print(f"❌ Không tìm thấy {package_name}")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm và tap {package_name}: {str(e)}")
            return False

    def press_key(self, keycode):
        """
        Gửi phím keyevent đến thiết bị.
        :param keycode: Mã phím (keycode) cần gửi
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"Gửi phím keyevent {keycode} đến thiết bị {self.serial}")
            # Sử dụng biến toàn cục ADB_PATH thay vì "adb" cứng
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", str(keycode)], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print(f"✅ Đã gửi phím keyevent {keycode}")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi gửi phím keyevent {keycode}: {str(e)}")
            return False

    def update_status(self, message):
        """
        Cập nhật trạng thái hiện tại.
        :param message: Thông báo trạng thái
        """
        # Chỉ in ra console vì không có giao diện để cập nhật
        print(f"📢 Trạng thái: {message}")

    def check_and_tap_ok(self, check_stop_flag=None, context=""):
        """
        Kiểm tra ok.png và nếu xuất hiện thì mở đa nhiệm (key 187), vuốt lên 2 lần để tắt ứng dụng và vào lại.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :param context: Ngữ cảnh để ghi log (ví dụ: "trong vòng lặp gói 1")
        :return: True nếu đã xử lý ok.png, False nếu không tìm thấy
        """
        if not os.path.exists(self.ok_template_path):
            return False

        # Kiểm tra ok.png với vòng lặp liên tục trong 5 giây để tăng hiệu quả phát hiện
        print(f"🔍 Đang kiểm tra ok.png {context}...")
        check_start = time.time()
        check_duration = 8.0  # Kiểm tra trong 8 giây

        while time.time() - check_start < check_duration:
            # Kiểm tra cờ dừng
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    return False

            # Kiểm tra ok.png với threshold thấp hơn để dễ phát hiện
            found, location = self.find_image_on_screen(self.ok_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
            if found:
                elapsed_time = time.time() - check_start
                print(f"✅ Phát hiện ok.png {context} sau {elapsed_time:.2f} giây - Thực hiện tắt ứng dụng và vào lại")
                print(f"📍 Vị trí ok.png: {location}")

                # Bước 1: Mở đa nhiệm (recent apps) bằng phím KEYCODE_APP_SWITCH
                print("📱 Mở đa nhiệm (recent apps) - key 187")
                if not self.press_key(187):  # KEYCODE_APP_SWITCH = 187
                    print("❌ Không thể mở đa nhiệm")
                    return False

                # Đợi đa nhiệm hiển thị
                if self._random_sleep(1.5, 2.0, "Chờ đa nhiệm hiển thị", check_stop_flag):
                    return False

                # Bước 2: Vuốt lên 2 lần để tắt ứng dụng
                print("🔄 Vuốt lên 2 lần để tắt ứng dụng")
                for swipe_count in range(2):
                    print(f"Vuốt lên lần {swipe_count + 1}/2")
                    if not self.swipe_up_to_close_app():
                        print(f"❌ Không thể vuốt lên lần {swipe_count + 1}")
                        return False
                    if self._random_sleep(0.8, 1.2, f"Chờ sau vuốt lên lần {swipe_count + 1}", check_stop_flag):
                        return False

                # Bước 3: Về màn hình chính
                print("🏠 Về màn hình chính")
                self.press_key(3)  # KEYCODE_HOME

                # Đợi về màn hình chính
                if self._random_sleep(1.0, 1.5, "Chờ về màn hình chính", check_stop_flag):
                    return False

                # Bước 4: Bắt đầu lại toàn bộ quy trình từ đầu đến cuối
                print("🔄 Bắt đầu lại toàn bộ quy trình mua từ đầu đến cuối")

                # Đợi một chút trước khi bắt đầu lại
                if self._random_sleep(2.0, 3.0, "Chờ trước khi bắt đầu lại quy trình", check_stop_flag):
                    return False

                # Gọi lại execute_actions() để bắt đầu từ đầu hoàn toàn
                return self.execute_actions(check_stop_flag)

            # Đợi ngắn trước khi kiểm tra lại
            time.sleep(0.2)

        total_time = time.time() - check_start
        print(f"ℹ️ Không tìm thấy ok.png {context} sau {total_time:.2f} giây kiểm tra liên tục")
        return False

    def check_and_tap_ok_mua(self, check_stop_flag=None, context=""):
        """
        Kiểm tra ok_mua.png và nếu xuất hiện thì tìm và tap vào nó (chỉ dùng trong vòng lặp gói).
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :param context: Ngữ cảnh để ghi log (ví dụ: "trong vòng lặp gói 1")
        :return: True nếu đã tìm thấy và tap ok_mua.png, False nếu không tìm thấy
        """
        template_dir = path_manager.get_template_path()
        ok_mua_template_path = os.path.join(template_dir, "ok_mua.png")

        if not os.path.exists(ok_mua_template_path):
            print(f"ℹ️ Không tìm thấy file ok_mua.png - bỏ qua kiểm tra")
            return False

        # Kiểm tra ok_mua.png với vòng lặp liên tục trong 3 giây
        print(f"🔍 Đang kiểm tra ok_mua.png {context}...")
        check_start = time.time()
        check_duration = 3.0  # Kiểm tra trong 3 giây

        while time.time() - check_start < check_duration:
            # Kiểm tra cờ dừng
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    return False

            # Kiểm tra ok_mua.png với threshold thấp hơn để dễ phát hiện
            found, location = self.find_image_on_screen(ok_mua_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
            if found:
                elapsed_time = time.time() - check_start
                print(f"✅ Phát hiện ok_mua.png {context} sau {elapsed_time:.2f} giây - Thực hiện tap")
                print(f"📍 Vị trí ok_mua.png: {location}")

                # Tap vào vị trí tìm thấy
                try:
                    subprocess.run(
                        [ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(location[0]), str(location[1])],
                        check=True,
                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                    )
                    print(f"✅ Đã tap vào ok_mua.png tại vị trí {location}")

                    # Đợi một chút sau khi tap
                    if self._random_sleep(1.0, 2.0, "Chờ sau khi tap ok_mua.png", check_stop_flag):
                        return False

                    return True
                except Exception as e:
                    print(f"❌ Lỗi khi tap vào ok_mua.png: {str(e)}")
                    return False

            # Đợi ngắn trước khi kiểm tra lại
            time.sleep(0.2)

        total_time = time.time() - check_start
        print(f"ℹ️ Không tìm thấy ok_mua.png {context} sau {total_time:.2f} giây kiểm tra liên tục")
        return False

    def swipe_up_from_bottom(self):
        """
        Vuốt lên từ dưới màn hình để mở navigation.
        """
        try:
            # Tọa độ vuốt từ dưới lên (giữa màn hình, từ dưới lên trên)
            start_x = self.screen_width // 2  # Giữa màn hình theo chiều ngang
            start_y = self.screen_height - 50  # Gần đáy màn hình
            end_x = self.screen_width // 2    # Giữa màn hình theo chiều ngang
            end_y = self.screen_height // 2   # Giữa màn hình theo chiều dọc

            print(f"📱 Vuốt lên từ ({start_x}, {start_y}) đến ({end_x}, {end_y})")
            subprocess.run([
                ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                str(start_x), str(start_y), str(end_x), str(end_y), "300"
            ], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return True
        except Exception as e:
            print(f"❌ Lỗi khi vuốt lên từ dưới màn hình: {str(e)}")
            return False

    def swipe_up_to_close_app(self):
        """
        Vuốt lên để đóng ứng dụng trong navigation.
        """
        try:
            # Tọa độ vuốt lên để đóng ứng dụng (từ giữa màn hình lên trên)
            start_x = self.screen_width // 2   # Giữa màn hình theo chiều ngang
            start_y = self.screen_height // 2  # Giữa màn hình theo chiều dọc
            end_x = self.screen_width // 2     # Giữa màn hình theo chiều ngang
            end_y = self.screen_height // 4    # 1/4 từ trên xuống

            print(f"🔄 Vuốt lên để đóng app từ ({start_x}, {start_y}) đến ({end_x}, {end_y})")
            subprocess.run([
                ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                str(start_x), str(start_y), str(end_x), str(end_y), "200"
            ], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return True
        except Exception as e:
            print(f"❌ Lỗi khi vuốt lên để đóng ứng dụng: {str(e)}")
            return False

    def exit_app_completely(self, check_stop_flag=None):
        """
        Mở đa nhiệm (recent apps) và vuốt lên 2 lần để thoát hoàn toàn ứng dụng khi hoàn thành mua
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🎯 Bắt đầu thoát hoàn toàn ứng dụng sau khi hoàn thành mua")

            # Bước 1: Mở đa nhiệm (recent apps) bằng phím KEYCODE_APP_SWITCH
            print("📱 Mở đa nhiệm (recent apps)")
            if not self.press_key(187):  # KEYCODE_APP_SWITCH = 187
                print("❌ Không thể mở đa nhiệm")
                return False

            # Đợi đa nhiệm hiển thị
            if self._random_sleep(1.5, 2.0, "Chờ đa nhiệm hiển thị", check_stop_flag):
                return False

            # Bước 2: Vuốt lên 2 lần để thoát ứng dụng
            print("🔄 Vuốt lên 2 lần để thoát hoàn toàn ứng dụng")
            for swipe_count in range(2):
                print(f"Vuốt lên lần {swipe_count + 1}/2")
                if not self.swipe_up_to_close_app():
                    print(f"❌ Không thể vuốt lên lần {swipe_count + 1}")
                    return False

                if self._random_sleep(0.8, 1.2, f"Chờ sau vuốt lên lần {swipe_count + 1}", check_stop_flag):
                    return False

            # Bước 3: Về màn hình chính
            print("🏠 Về màn hình chính sau khi thoát ứng dụng")
            self.press_key(3)  # KEYCODE_HOME

            if self._random_sleep(1.0, 1.5, "Chờ về màn hình chính", check_stop_flag):
                return False

            print("✅ Đã thoát hoàn toàn ứng dụng thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thoát hoàn toàn ứng dụng: {str(e)}")
            return False

    def restart_app_flow(self, check_stop_flag=None):
        """
        Vào lại ứng dụng như ban đầu (từ bước 1-4).
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    return check_stop_flag()
                return False

            print("🔄 Bắt đầu vào lại ứng dụng...")

            # Bước 1: Về màn hình chính
            print("🏠 Về màn hình chính")
            self.press_key(3)  # KEYCODE_HOME
            if self._random_sleep(1.0, 2.0, "Chờ về màn hình chính", check_stop_flag):
                return False

            # Bước 2: Tìm và tap vào game.png
            print("🎮 Tìm và tap vào game.png")
            template_dir = path_manager.get_template_path()
            game_template_path = os.path.join(template_dir, "game.png")

            if not os.path.exists(game_template_path):
                print(f"❌ Không tìm thấy file template game.png")
                return False

            # Thử tìm game.png tối đa 3 lần
            game_found = False
            for attempt in range(3):
                if should_stop():
                    return False

                print(f"Lần thử {attempt + 1}/3 tìm game.png")
                if self.find_image_and_tap(game_template_path, check_stop_flag=check_stop_flag):
                    game_found = True
                    print("✅ Đã tìm thấy và tap vào game.png")
                    break
                else:
                    if attempt < 2:
                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                            return False

            if not game_found:
                print("❌ Không thể tìm thấy game.png sau 3 lần thử")
                return False

            # Đợi ứng dụng khởi động
            if self._random_sleep(3.0, 5.0, "Chờ ứng dụng khởi động", check_stop_flag):
                return False

            # Bước 3: Tìm và tap vào nap_goi.png
            print("💰 Tìm và tap vào nap_goi.png")
            nap_goi_template_path = os.path.join(template_dir, "nap_goi.png")

            if not os.path.exists(nap_goi_template_path):
                print(f"❌ Không tìm thấy file template nap_goi.png")
                return False

            # Thử tìm nap_goi.png tối đa 3 lần
            nap_goi_found = False
            for attempt in range(3):
                if should_stop():
                    return False

                print(f"Lần thử {attempt + 1}/3 tìm nap_goi.png")
                if self.find_image_and_tap(nap_goi_template_path, check_stop_flag=check_stop_flag):
                    nap_goi_found = True
                    print("✅ Đã tìm thấy và tap vào nap_goi.png")
                    break
                else:
                    if attempt < 2:
                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                            return False

            if not nap_goi_found:
                print("❌ Không thể tìm thấy nap_goi.png sau 3 lần thử")
                return False

            # Đợi sau khi tap vào nap_goi
            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào nap_goi.png", check_stop_flag):
                return False

            print("✅ Đã hoàn thành vào lại ứng dụng")
            print("✅ Sẵn sàng để tiếp tục quy trình mua từ đầu")

            # Không gọi perform_action_sequence ở đây để tránh tắt app 2 lần
            # Thay vào đó, return True để báo hiệu đã restart thành công
            # Quy trình mua sẽ được tiếp tục từ nơi gọi check_and_tap_ok()
            return True

        except Exception as e:
            print(f"❌ Lỗi khi vào lại ứng dụng: {str(e)}")
            return False

    def _random_sleep(self, min_sec=0.5, max_sec=1.0, action_desc="", check_stop_flag=None):
        """
        Tạm dừng thực thi trong khoảng thời gian ngẫu nhiên.
        :param min_sec: Thời gian tối thiểu (giây)
        :param max_sec: Thời gian tối đa (giây)
        :param action_desc: Mô tả hành động đang thực hiện
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu đã dừng do cờ dừng, False nếu đã ngủ hết thời gian
        """
        # Điều chỉnh thời gian chờ để cân bằng giữa tốc độ và độ chính xác
        # Thêm device-specific delay để tránh conflict giữa các thiết bị
        device_offset = (self.device_index * 0.1) if hasattr(self, 'device_index') else 0
        t = random.uniform(min_sec + device_offset, max_sec + device_offset)
        print(f"⏳ {action_desc}... (chờ {t:.2f}s, thiết bị {self.device_index if hasattr(self, 'device_index') else 'N/A'})")

        # Nếu có hàm kiểm tra cờ dừng, kiểm tra mỗi 0.05 giây
        if check_stop_flag and callable(check_stop_flag):
            start_time = time.time()
            end_time = start_time + t

            while time.time() < end_time:
                # Kiểm tra cờ dừng
                if check_stop_flag():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đang chờ")
                    # Thông báo thêm để dễ theo dõi
                    print(f"⚠️ Dừng quá trình ngủ tại {action_desc if action_desc else 'không có thông báo'}")
                    return True  # Đã dừng do cờ dừng

                # Ngủ một khoảng thời gian ngắn hơn để phản ứng nhanh hơn với cờ dừng
                time.sleep(0.05)

            return False  # Đã ngủ hết thời gian
        else:
            # Nếu không có hàm kiểm tra cờ dừng, ngủ bình thường
            time.sleep(t)
            return False

    def find_image_on_screen(self, template_path, threshold=0.8, roi=None, check_stop_flag=None):
        """
        Tìm hình ảnh trên màn hình nhưng không tap vào.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param roi: Region of Interest - Vùng quan tâm [x, y, width, height]. Nếu None, tìm trên toàn bộ màn hình.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: (True, location) nếu tìm thấy, (False, None) nếu không.
        """
        try:
            # Kiểm tra cờ dừng nếu có
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm {os.path.basename(template_path)}")
                    return False, None

            # Bắt đầu đo thời gian
            start_time = time.time()

            # Chụp ảnh màn hình với timeout protection
            print(f"📸 Đang chụp ảnh màn hình để tìm {os.path.basename(template_path)} (không tap)...")
            screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")

            # Sử dụng exec-out với timeout để tăng tốc độ chụp ảnh và tránh hang
            try:
                with open(screenshot_path, "wb") as f:
                    subprocess.run([ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                                    stdout=f, check=True, timeout=10,  # Thêm timeout 10 giây
                                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            except subprocess.TimeoutExpired:
                print(f"⚠️ Timeout khi chụp ảnh màn hình thiết bị {self.serial}")
                return False, None
            except Exception as e:
                print(f"❌ Lỗi khi chụp ảnh màn hình thiết bị {self.serial}: {str(e)}")
                return False, None

            capture_time = time.time() - start_time
            print(f"✅ Đã chụp ảnh màn hình thành công: {screenshot_path} (thời gian: {capture_time:.2f}s)")

            # Kiểm tra cờ dừng sau khi chụp ảnh
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi chụp ảnh màn hình")
                    return False, None

            # Đợi một chút sau khi chụp ảnh để đảm bảo file đã được lưu hoàn toàn
            # Tăng thời gian chờ để đảm bảo ổn định trên nhiều thiết bị
            time.sleep(0.8)

            # Đọc ảnh template và ảnh màn hình với retry mechanism
            template = cv2.imread(template_path)
            screenshot = None

            # Retry đọc screenshot tối đa 3 lần để tăng độ ổn định
            for retry in range(3):
                screenshot = cv2.imread(screenshot_path)
                if screenshot is not None:
                    break
                print(f"⚠️ Lần thử {retry + 1}/3: Không thể đọc ảnh màn hình, đợi và thử lại...")
                time.sleep(0.5)

            if template is None:
                print(f"❌ Không thể đọc ảnh template: {template_path}")
                # Kiểm tra kích thước file template
                try:
                    template_size = os.path.getsize(template_path)
                    print(f"ℹ️ Kích thước file template: {template_size} bytes")
                    if template_size == 0:
                        print(f"❌ File template có kích thước 0 bytes")
                except Exception as e:
                    print(f"❌ Lỗi khi kiểm tra kích thước file template: {str(e)}")
                return False, None

            if screenshot is None:
                print(f"❌ Không thể đọc ảnh màn hình sau 3 lần thử: {screenshot_path}")
                # Kiểm tra kích thước file screenshot
                try:
                    screenshot_size = os.path.getsize(screenshot_path)
                    print(f"ℹ️ Kích thước file screenshot: {screenshot_size} bytes")
                    if screenshot_size == 0:
                        print(f"❌ File screenshot có kích thước 0 bytes")
                except Exception as e:
                    print(f"❌ Lỗi khi kiểm tra kích thước file screenshot: {str(e)}")
                return False, None

            # In thông tin về kích thước ảnh để debug
            print(f"ℹ️ Kích thước template: {template.shape}")
            print(f"ℹ️ Kích thước screenshot: {screenshot.shape}")

            # Chuyển đổi ảnh sang thang độ xám
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # Lấy kích thước template
            template_h, template_w = template_gray.shape

            # Nếu có ROI, cắt ảnh màn hình theo ROI
            if roi:
                x, y, w, h = roi
                screenshot_gray = screenshot_gray[y:y+h, x:x+w]
                roi_offset_x, roi_offset_y = x, y
            else:
                roi_offset_x, roi_offset_y = 0, 0

            # Tìm kiếm template trong ảnh màn hình
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Tính thời gian tổng cộng
            total_time = time.time() - start_time

            # Nếu độ khớp lớn hơn ngưỡng, trả về True và vị trí
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên màn hình
                center_x = roi_offset_x + max_loc[0] + template_w // 2
                center_y = roi_offset_y + max_loc[1] + template_h // 2

                print(f"✅ Đã tìm thấy {os.path.basename(template_path)} tại ({center_x}, {center_y}) (độ khớp: {max_val:.2f}, thời gian: {total_time:.2f}s)")

                # Memory cleanup để tránh memory leak
                del template, screenshot, template_gray, screenshot_gray, result

                return True, (center_x, center_y)
            else:
                print(f"❌ Không tìm thấy {os.path.basename(template_path)} (max_val = {max_val:.2f}, thời gian: {total_time:.2f}s)")

                # Memory cleanup để tránh memory leak
                del template, screenshot, template_gray, screenshot_gray, result

                return False, None

        except Exception as e:
            print(f"❌ Lỗi khi tìm {os.path.basename(template_path)}: {str(e)}")
            # Error recovery: Thử cleanup memory nếu có lỗi
            try:
                if 'template' in locals():
                    del template
                if 'screenshot' in locals():
                    del screenshot
                if 'template_gray' in locals():
                    del template_gray
                if 'screenshot_gray' in locals():
                    del screenshot_gray
                if 'result' in locals():
                    del result
            except:
                pass
            return False, None

    def find_image_and_tap(self, template_path, threshold=0.8, check_stop_flag=None, scale_factor=0.5):
        """
        Tìm vị trí ảnh trên màn hình và tap vào đó với xử lý ảnh nhẹ hơn.
        :param template_path: Đường dẫn đến file ảnh template
        :param threshold: Ngưỡng độ khớp (0.0-1.0)
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :param scale_factor: Hệ số thu nhỏ ảnh để tăng tốc xử lý (0.0-1.0)
        :return: True nếu tìm thấy và tap thành công, False nếu không tìm thấy
        """
        # Hàm kiểm tra cờ dừng
        def should_stop():
            if check_stop_flag and callable(check_stop_flag):
                stop_flag = check_stop_flag()
                if stop_flag:
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi tìm {os.path.basename(template_path)}")
                return stop_flag
            return False

        try:
            print(f"Tìm và tap vào ảnh: {os.path.basename(template_path)}")

            # Kiểm tra cờ dừng và file template một lần duy nhất trước khi bắt đầu
            if should_stop() or not os.path.exists(template_path):
                if not os.path.exists(template_path):
                    print(f"❌ Không tìm thấy file template: {template_path}")
                return False

            # Chụp màn hình thiết bị trực tiếp vào bộ nhớ
            try:
                # Sử dụng pipe để tránh ghi file tạm
                process = subprocess.Popen(
                    [ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                screenshot_data, _ = process.communicate()

                # Chuyển đổi dữ liệu ảnh thành mảng numpy
                screenshot_array = np.frombuffer(screenshot_data, dtype=np.uint8)
                screenshot = cv2.imdecode(screenshot_array, cv2.IMREAD_COLOR)

                # Bỏ lưu ảnh debug để tăng performance
            except Exception as e:
                print(f"❌ Lỗi khi chụp màn hình: {str(e)}")
                return False

            if should_stop():
                return False

            # Đọc ảnh template
            template = cv2.imread(template_path)

            if template is None or screenshot is None:
                print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình")
                return False

            # Thu nhỏ ảnh để tăng tốc xử lý
            if scale_factor < 1.0:
                new_width = int(screenshot.shape[1] * scale_factor)
                new_height = int(screenshot.shape[0] * scale_factor)
                screenshot_resized = cv2.resize(screenshot, (new_width, new_height), interpolation=cv2.INTER_AREA)

                new_width = int(template.shape[1] * scale_factor)
                new_height = int(template.shape[0] * scale_factor)
                template_resized = cv2.resize(template, (new_width, new_height), interpolation=cv2.INTER_AREA)
            else:
                screenshot_resized = screenshot
                template_resized = template

            # Chuyển đổi ảnh sang thang độ xám để giảm kích thước dữ liệu
            template_gray = cv2.cvtColor(template_resized, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot_resized, cv2.COLOR_BGR2GRAY)

            # Tìm kiếm template trong ảnh màn hình với phương pháp hiệu quả
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên ảnh gốc
                h, w = template_gray.shape
                center_x = int(max_loc[0] / scale_factor + w / scale_factor / 2)
                center_y = int(max_loc[1] / scale_factor + h / scale_factor / 2)

                print(f"✅ Đã tìm thấy ảnh với độ khớp {max_val:.2f} tại vị trí ({center_x}, {center_y})")

                if should_stop():
                    return False

                # Tap vào vị trí tìm thấy
                subprocess.run(
                    [ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(center_x), str(center_y)],
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                print(f"✅ Đã tap vào vị trí ({center_x}, {center_y})")

                # Đợi một chút sau khi tap
                if self._random_sleep(1.0, 2.0, "Chờ sau khi tap", check_stop_flag):
                    return False

                return True
            else:
                print(f"❌ Không tìm thấy ảnh với độ khớp đủ cao (max_val = {max_val:.2f})")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm và tap vào ảnh: {str(e)}")
            return False

    def perform_action_sequence(self, check_stop_flag=None, buy_pass_processed=False):
        """Thực hiện chuỗi hành động chính"""
        try:
            def should_stop():
                return check_stop_flag and callable(check_stop_flag) and check_stop_flag()

            print("🚀 Bắt đầu chuỗi hành động chính")

            # Thực hiện 8 vòng lặp chính
            print("🔄 BẮT ĐẦU THỰC HIỆN 8 VÒNG LẶP CHÍNH")
            main_loop_result = self.execute_main_loop(check_stop_flag, buy_pass_processed)
            print(f"🔄 KẾT QUẢ VÒNG LẶP CHÍNH: {main_loop_result}")
            print(f"🔍 DEBUG: main_loop_result type = {type(main_loop_result)}, value = '{main_loop_result}'")

            if main_loop_result == "no_more_cards":
                print("🏠 ĐÃ HẾT THẺ TRONG VÒNG LẶP CHÍNH - KHÔNG TÌM THẤY buy.png SAU KHI TAP TỌA ĐỘ THẺ")
                print("🏠 BỎ QUA VÒNG LẶP PHỤ (KHÔNG VUỐT LÊN)")
                print("🏠 KẾT THÚC THÀNH CÔNG MÀ KHÔNG CẦN VÒNG LẶP PHỤ")

                # Thoát hoàn toàn ứng dụng khi hoàn thành mua
                print("🎯 Hoàn thành mua - Thoát hoàn toàn ứng dụng")
                self.exit_app_completely(check_stop_flag)

                return True  # Kết thúc thành công mà không cần vòng lặp phụ
            elif not main_loop_result:
                print("❌ VÒNG LẶP CHÍNH THẤT BẠI")
                return False

            # Thực hiện 9 vòng lặp phụ để xử lý các thẻ ẩn phía dưới (chỉ khi còn thẻ)
            print("🔄 VẪN CÒN THẺ, TIẾP TỤC VỚI VÒNG LẶP PHỤ ĐỂ TÌM THẺ ẨN (SẼ VUỐT LÊN)")
            aux_result = self.execute_auxiliary_loops(check_stop_flag)

            if aux_result == "no_more_hidden_cards":
                print("🏠 ĐÃ HẾT THẺ ẨN TRONG VÒNG LẶP PHỤ")
                print("🏠 KẾT THÚC THÀNH CÔNG")

                # Thoát hoàn toàn ứng dụng khi hoàn thành mua
                print("🎯 Hoàn thành mua - Thoát hoàn toàn ứng dụng")
                self.exit_app_completely(check_stop_flag)

                return True
            elif not aux_result:
                return False

            # Tăng biến loop_count sau khi hoàn thành một vòng lặp
            self.loop_count += 1

            print(f"✅ Đã hoàn thành vòng lặp (bao gồm cả vòng lặp phụ)")

            # Thoát hoàn toàn ứng dụng khi hoàn thành mua
            print("🎯 Hoàn thành mua - Thoát hoàn toàn ứng dụng")
            self.exit_app_completely(check_stop_flag)

            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False

    def execute_actions(self, check_stop_flag=None):
        """
        Thực hiện chuỗi hành động: về màn hình chính, mở menu, tìm và tap vào game.png
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng từ MainForm.py
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"🚀 Bắt đầu thực hiện chuỗi hành động trên thiết bị {self.serial}")
            print(f"📋 Quy trình: Từ đầu đến cuối (Bước 1-8 + Vòng lặp chính + Vòng lặp phụ)")

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    # Kiểm tra cờ dừng
                    stop_flag = check_stop_flag()
                    if stop_flag:
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng")
                    return stop_flag
                return False

            # Kiểm tra cờ dừng trước khi bắt đầu
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi bắt đầu thực hiện các bước")
                return False

            # Bước 1: Về màn hình chính bằng cách nhấn nút Home
            print("Bước 1: Về màn hình chính bằng cách nhấn nút Home")
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "3"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")

            # Đợi một chút sau khi về màn hình chính
            if self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi về màn hình chính")
                return False

            # Bước 2: Vuốt lên để hiển thị menu ứng dụng
            print("Bước 2: Vuốt lên để hiển thị menu ứng dụng")
            # Lấy kích thước màn hình
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt từ dưới lên trên
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                          str(screen_width // 2), str(screen_height * 4 // 5),
                          str(screen_width // 2), str(screen_height // 5), "300"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print("✅ Đã vuốt lên để hiển thị menu ứng dụng")

            # Đợi một chút sau khi vuốt
            if self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên để hiển thị menu ứng dụng", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi vuốt lên")
                return False

            # Bước 3: Tìm và tap vào game.png
            print("Bước 3: Tìm và tap vào game.png")
            template_dir = path_manager.get_template_path()
            game_template_path = os.path.join(template_dir, "game.png")

            # Kiểm tra xem file template game.png có tồn tại không
            if not os.path.exists(game_template_path):
                print(f"⚠️ Không tìm thấy file template game.png tại {game_template_path}")
                print(f"⚠️ Vui lòng đặt file game.png vào thư mục {template_dir}")
                return False

            # Thử tìm game.png tối đa 5 lần với vuốt lên giữa các lần thử
            game_found = False
            for attempt in range(5):
                # Kiểm tra cờ dừng trước mỗi lần thử
                if should_stop():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/5 tìm game.png")
                    return False

                print(f"Lần thử {attempt + 1}/5 tìm game.png")
                if self.find_image_and_tap(game_template_path, check_stop_flag=check_stop_flag):
                    game_found = True
                    print("✅ Đã tìm thấy và tap vào game.png")
                    break
                else:
                    print(f"❌ Không tìm thấy game.png trong lần thử {attempt + 1}")

                    # Nếu không tìm thấy và chưa phải lần thử cuối cùng, vuốt lên và thử lại
                    if attempt < 4:
                        print("Vuốt lên để tìm tiếp")

                        # Vuốt từ dưới lên trên
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                                      str(screen_width // 2), str(screen_height * 4 // 5),
                                      str(screen_width // 2), str(screen_height // 5), "300"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                        print(f"✅ Đã vuốt lên lần {attempt + 1}")

                        # Đợi một chút sau khi vuốt
                        if self._random_sleep(1.0, 2.0, f"Chờ sau khi vuốt lên lần {attempt + 1}", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi vuốt lên lần {attempt + 1}")
                            return False

            # Nếu không tìm thấy game.png sau tất cả các lần thử, thông báo lỗi và dừng
            if not game_found:
                print("❌ Không thể tìm thấy game.png sau 5 lần thử")
                return False

            # Đợi ứng dụng khởi động
            if self._random_sleep(3.0, 5.0, "Chờ ứng dụng game khởi động", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ ứng dụng game khởi động")
                return False

            # Bước 4: Tìm và tap vào game1.png
            print("Bước 4: Tìm và tap vào game1.png")
            game1_found = False

            if os.path.exists(self.game1_template_path):
                game1_found = self.find_image_and_tap(self.game1_template_path, check_stop_flag=should_stop)
                if game1_found:
                    print("✅ Đã tìm thấy và tap vào game1.png")
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào game1.png", check_stop_flag):
                        return False
            else:
                print("ℹ️ Không tìm thấy game1.png, thực hiện bước tiếp theo")

            # Bước 5: Tìm và tap vào nap_goi.png
            print("Bước 5: Tìm và tap vào nap_goi.png")
            nap_goi_found = False
            if os.path.exists(self.nap_goi_template_path):
                nap_goi_found = self.find_image_and_tap(self.nap_goi_template_path, check_stop_flag=should_stop)
                if nap_goi_found:
                    print("✅ Đã tìm thấy và tap vào nap_goi.png")
                    if self._random_sleep(5.0, 6.0, "Chờ sau khi tap vào nap_goi.png", check_stop_flag):
                        return False
            else:
                print("ℹ️ Không tìm thấy nap_goi.png, thực hiện bước tiếp theo")

            # Đợi web load sau khi tap vào nap_goi.png (thời gian chờ dài hơn vì cần load web)
            if self._random_sleep(4.0, 5.0, "Chờ web load sau khi tap vào nap_goi.png", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ web load sau khi tap vào nap_goi.png")
                return False

            # Bước 6: Kiểm tra ok.png và retart lại từ đầu nếu có
            print("Bước 6: Kiểm tra xem có ok.png xuất hiện không")
            ok_handled = self.check_and_tap_ok(check_stop_flag, "sau khi tap goi1.png")
            if ok_handled:
                print("✅ Đã xử lý ok.png bằng cách tắt ứng dụng và vào lại")
                print("🔄 Tiếp tục thực hiện quy trình mua từ bước 7 sau khi restart")
                # Retart lại từ đầu
            else:
                print("ℹ️ Không phát hiện ok.png - tiếp tục quy trình bình thường")

            # Bước 7: Tìm và tap vào goi1.png
            print("Bước 7: Tìm và tap vào goi1.png")

            # Kiểm tra trước khi tìm goi1.png
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm goi1.png")
                return False

            # Tìm và tap vào goi1.png
            goi1_tapped = self.find_and_tap_package(
                self.goi1_template_path,
                "Gói 1 (Bước 6)",
                1,
                1,
                check_stop_flag
            )

            if goi1_tapped:
                print("✅ Đã tìm thấy và tap vào goi1.png")
            else:
                print("❌ Không thể tìm thấy goi1.png")
                print("ℹ️ Tiếp tục quy trình mặc dù không tìm thấy goi1.png")

            # Đợi sau khi tap vào goi1.png
            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào goi1.png", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào goi1.png")
                return False

            # Bước 8: Tìm và tap vào buy_pass.png nếu xuất hiện
            print("Bước 8: Kiểm tra và tap vào buy_pass.png nếu xuất hiện")
            buy_pass_template_path = os.path.join(template_dir, "buy_pass.png")
            buy_pass_processed = False  # Flag để theo dõi xem buy_pass đã được xử lý

            # Kiểm tra xem file template buy_pass.png có tồn tại không
            if os.path.exists(buy_pass_template_path):
                # Thử tìm buy_pass.png tối đa 3 lần
                buy_pass_found = False
                for attempt in range(3):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/3 tìm buy_pass.png")
                        return False

                    print(f"Lần thử {attempt + 1}/3 tìm buy_pass.png")
                    if self.find_image_and_tap(buy_pass_template_path, check_stop_flag=check_stop_flag):
                        buy_pass_found = True
                        buy_pass_processed = True  # Đánh dấu đã xử lý buy_pass
                        print("✅ Đã tìm thấy và tap vào buy_pass.png")

                        # Đợi sau khi tap vào buy_pass.png
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào buy_pass.png", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào buy_pass.png")
                            return False

                        # Xử lý nhập mật khẩu
                        print("🔑 Bắt đầu xử lý nhập mật khẩu")

                        # Ưu tiên lấy mật khẩu từ giao diện (cột pass)
                        password_from_ui = getattr(self, 'ui_password', None)
                        if password_from_ui and password_from_ui.strip():
                            print(f"🔑 Sử dụng mật khẩu từ giao diện: {password_from_ui[:2]}{'*' * (len(password_from_ui) - 2)}")
                            password_to_use = password_from_ui
                        else:
                            # Nếu không có mật khẩu từ giao diện, sử dụng mật khẩu từ file mail.txt
                            print(f"🔑 Sử dụng mật khẩu từ file mail.txt: {self.password[:2] if self.password else 'Không có'}{'*' * (len(self.password) - 2) if self.password else ''}")
                            password_to_use = self.password

                        if password_to_use:
                            print(f"🔑 Thiết bị {self.serial} (index {self.device_index}) sẽ nhập mật khẩu: {password_to_use[:2]}{'*' * (len(password_to_use) - 2)}")

                            # Nhập mật khẩu (không cần tap vào vị trí nào)
                            try:
                                # Đợi một chút để giao diện sẵn sàng nhận input
                                print("⏳ Đợi giao diện sẵn sàng nhận password...")
                                if self._random_sleep(2.0, 3.0, "Chờ giao diện sẵn sàng nhận password", check_stop_flag):
                                    return False

                                # Xóa nội dung cũ
                                print("🔄 Xóa nội dung cũ trong trường nhập liệu")
                                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "113"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_CTRL_A
                                time.sleep(0.2)
                                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_DEL
                                time.sleep(0.2)

                                # Nhập mật khẩu (loại bỏ ký tự | gây lỗi)
                                clean_password = password_to_use.replace('|', '')
                                text_for_adb = clean_password.replace(' ', '%s')
                                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", text_for_adb],
                                             check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                print(f"✅ Đã nhập mật khẩu thành công: {clean_password[:2]}{'*' * (len(clean_password) - 2)}")

                            except Exception as e:
                                print(f"❌ Lỗi khi nhập mật khẩu: {str(e)}")

                            # Đợi sau khi nhập mật khẩu
                            if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập mật khẩu", check_stop_flag):
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập mật khẩu")
                                return False

                            # Tìm và tap vào checkbox.png
                            print("🔍 Tìm và tap vào checkbox.png")
                            checkbox_template_path = os.path.join(template_dir, "checkbox.png")
                            if os.path.exists(checkbox_template_path):
                                if self.find_image_and_tap(checkbox_template_path, check_stop_flag=check_stop_flag):
                                    print("✅ Đã tap vào checkbox.png")
                                    if self._random_sleep(1.0, 2.0, "Chờ sau khi tap checkbox", check_stop_flag):
                                        return False

                            # Tìm và tap vào verify.png
                            print("🔍 Tìm và tap vào verify.png")
                            verify_template_path = os.path.join(template_dir, "verify.png")
                            if os.path.exists(verify_template_path):
                                if self.find_image_and_tap(verify_template_path, check_stop_flag=check_stop_flag):
                                    print("✅ Đã tap vào verify.png")
                                    if self._random_sleep(6.0, 7.0, "Chờ sau khi tap verify", check_stop_flag):
                                        return False

                                    # Tìm và tap vào agree.png nếu có
                                    print("🔍 Tìm và tap vào agree.png nếu có")
                                    if os.path.exists(self.agree_template_path):
                                        if self.find_image_and_tap(self.agree_template_path, check_stop_flag=check_stop_flag):
                                            print("✅ Đã tap vào agree.png")
                                            if self._random_sleep(1.0, 2.0, "Chờ sau khi tap agree", check_stop_flag):
                                                return False
                        else:
                            print("⚠️ Không có mật khẩu để nhập. Vui lòng kiểm tra lại.")

                        break
                    else:
                        print(f"❌ Không tìm thấy buy_pass.png trong lần thử {attempt + 1}")
                        if attempt < 2:
                            if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                return False

                if not buy_pass_found:
                    print("ℹ️ Không tìm thấy buy_pass.png, chuyển sang tìm buy.png")
            else:
                print("ℹ️ Không tìm thấy file template buy_pass.png, chuyển sang tìm buy.png")

            # Thực hiện chuỗi hành động chính
            return self.perform_action_sequence(check_stop_flag, buy_pass_processed)

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False

    def execute_main_loop(self, check_stop_flag=None, buy_pass_processed=False):
        """Thực hiện vòng lặp chính với 8 vòng lặp + 9 vòng lặp phụ"""
        try:
            def should_stop():
                return check_stop_flag and callable(check_stop_flag) and check_stop_flag()

            # Lấy đường dẫn template
            template_dir = path_manager.get_template_path()
            buy_template_path = os.path.join(template_dir, "buy.png")
            success_template_path = os.path.join(template_dir, "success.png")
            tap_join_template_path = os.path.join(template_dir, "tap_join.png")

            # Tọa độ tap cho 8 vòng lặp chính
            tap_coordinates = [
                (352, 823), (378, 974), (399, 1155),(352,1339),
                (352, 1509),(387, 1683),(350, 1863),(510,2010)
            ]

            max_iterations = 8  # 8 vòng lặp chính

            print(f"🔄 Bắt đầu {max_iterations} vòng lặp chính")

            for iteration in range(1, max_iterations + 1):
                # Cập nhật thứ tự thẻ (vòng lặp đầu tiên là thẻ thứ 2)
                self.current_card_number = iteration + 1
                if should_stop():
                    print(f"⛔ Đã nhận tín hiệu dừng tại vòng lặp chính {iteration}")
                    return False

                print(f"🔄 Vòng lặp chính {iteration}/{max_iterations}")

                # Tính toán tọa độ cho lần này (sẽ dùng ở cuối vòng lặp)
                coord = tap_coordinates[(iteration - 1) % len(tap_coordinates)]
                x, y = coord

                # Bước đầu: Kiểm tra xem có buy.png hiện tại không (trước khi tap tọa độ)
                # Nếu buy_pass đã được xử lý thì bỏ qua bước tìm buy.png CHỈ Ở VÒNG LẶP ĐẦU TIÊN
                # NHƯNG vẫn kiểm tra success.png và tap_join.png
                skip_buy_due_to_pass = False  # Flag để theo dõi việc bỏ qua buy.png do buy_pass
                if buy_pass_processed and iteration == 1:
                    print(f"ℹ️ Bỏ qua kiểm tra buy.png ở vòng lặp đầu tiên vì buy_pass.png đã được xử lý")
                    print("ℹ️ NHƯNG vẫn kiểm tra success.png và tap_join.png để vào vòng lặp gói")
                    buy_found = False  # Đặt buy_found = False để bỏ qua xử lý buy.png
                    skip_buy_due_to_pass = True  # Đánh dấu đã bỏ qua do buy_pass

                    # Kiểm tra success.png ngay lập tức (vì buy_pass đã được xử lý)
                    success_found = False
                    if os.path.exists(success_template_path):
                        print("🔍 Kiểm tra success.png sau khi xử lý buy_pass...")
                        found, _ = self.find_image_on_screen(success_template_path, threshold=0.6, check_stop_flag=check_stop_flag)
                        if found:
                            success_found = True
                            print(f"✅ Phát hiện success.png sau khi xử lý buy_pass - Thực hiện vòng lặp gói")
                            package_result = self.execute_package_loops(check_stop_flag)
                            if package_result:
                                print("✅ Hoàn thành vòng lặp gói, tiếp tục vòng lặp chính")
                            else:
                                print("⚠️ Vòng lặp gói không thành công, nhưng vẫn tiếp tục vòng lặp chính")

                    # Nếu không tìm thấy success.png, kiểm tra tap_join.png
                    if not success_found and os.path.exists(tap_join_template_path):
                        tap_join_found, _ = self.find_image_on_screen(tap_join_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                        if tap_join_found:
                            print("✅ Phát hiện tap_join.png sau khi xử lý buy_pass - Tap và thực hiện vòng lặp gói")
                            if self.find_image_and_tap(tap_join_template_path, threshold=0.7, check_stop_flag=check_stop_flag):
                                # Đợi và tìm got_it.png
                                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap tap_join.png", check_stop_flag):
                                    return False

                                got_it_template_path = os.path.join(template_dir, "got_it.png")
                                if os.path.exists(got_it_template_path):
                                    if self.find_image_and_tap(got_it_template_path, check_stop_flag=check_stop_flag):
                                        print("✅ Đã tap vào got_it.png")

                                # Thực hiện vòng lặp gói
                                package_result = self.execute_package_loops(check_stop_flag)
                                if package_result:
                                    print("✅ Hoàn thành vòng lặp gói, tiếp tục vòng lặp chính")
                                else:
                                    print("⚠️ Vòng lặp gói không thành công, nhưng vẫn tiếp tục vòng lặp chính")

                    # Reset flag sau vòng lặp đầu tiên để các vòng lặp sau tìm buy.png bình thường
                    buy_pass_processed = False
                    print("🔄 Đã reset flag buy_pass_processed - các vòng lặp tiếp theo sẽ tìm buy.png bình thường")
                else:
                    print(f"🔍 Bước đầu vòng lặp {iteration}: Kiểm tra buy.png hiện tại")
                    buy_found = False
                    for attempt in range(2):  # Thử tối đa 2 lần
                        if should_stop():
                            print(f"⚠️ Dừng trong lần thử {attempt + 1}/2 tìm buy.png sau khi tap tọa độ")
                            return False

                        print(f"Lần thử {attempt + 1}/2 tìm buy.png hiện tại")
                        found, _ = self.find_image_on_screen(buy_template_path, check_stop_flag=check_stop_flag)
                        if found:
                            buy_found = True
                            print("✅ Đã tìm thấy buy.png hiện tại")

                            # Tap vào buy.png
                            if self.find_image_and_tap(buy_template_path, check_stop_flag=check_stop_flag):
                                print("✅ Đã tap vào buy.png")

                                # Đợi 4 giây trước khi kiểm tra success.png (vì success xuất hiện sau 4s)
                                print("⏳ Đợi 4 giây để success.png xuất hiện...")
                                if self._random_sleep(4.0, 4.5, "Chờ success.png xuất hiện sau khi tap buy", check_stop_flag):
                                    return False

                                # Kiểm tra success.png liên tục trong 6 giây (vì success chỉ hiển thị 3s)
                                success_found = False
                                if os.path.exists(success_template_path):
                                    print("🔍 Kiểm tra success.png liên tục trong 8 giây...")
                                    check_start = time.time()
                                    check_duration = 8.0  # Kiểm tra trong 8 giây

                                    while time.time() - check_start < check_duration:
                                        if check_stop_flag and callable(check_stop_flag) and check_stop_flag():
                                            return False

                                        found, _ = self.find_image_on_screen(success_template_path, threshold=0.6, check_stop_flag=check_stop_flag)
                                        if found:
                                            success_found = True
                                            elapsed_time = time.time() - check_start
                                            print(f"✅ Phát hiện success.png sau {elapsed_time:.2f} giây - Thực hiện vòng lặp gói")
                                            package_result = self.execute_package_loops(check_stop_flag)
                                            if package_result:
                                                print("✅ Hoàn thành vòng lặp gói, tiếp tục vòng lặp chính")
                                            else:
                                                print("⚠️ Vòng lặp gói không thành công, nhưng vẫn tiếp tục vòng lặp chính")
                                            break

                                        # Đợi ngắn trước khi kiểm tra lại
                                        time.sleep(0.2)

                                    if not success_found:
                                        total_time = time.time() - check_start
                                        print(f"ℹ️ Không tìm thấy success.png sau {total_time:.2f} giây kiểm tra liên tục")

                                # Nếu không tìm thấy success.png, kiểm tra tap_join.png
                                if not success_found and os.path.exists(tap_join_template_path):
                                    tap_join_found, _ = self.find_image_on_screen(tap_join_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                                    if tap_join_found:
                                        print("✅ Phát hiện tap_join.png - Tap và thực hiện vòng lặp gói")
                                        if self.find_image_and_tap(tap_join_template_path, threshold=0.7, check_stop_flag=check_stop_flag):
                                            # Đợi và tìm got_it.png
                                            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap tap_join.png", check_stop_flag):
                                                return False

                                            got_it_template_path = os.path.join(template_dir, "got_it.png")
                                            if os.path.exists(got_it_template_path):
                                                if self.find_image_and_tap(got_it_template_path, check_stop_flag=check_stop_flag):
                                                    print("✅ Đã tap vào got_it.png")

                                            # Thực hiện vòng lặp gói
                                            package_result = self.execute_package_loops(check_stop_flag)
                                            if package_result:
                                                print("✅ Hoàn thành vòng lặp gói, tiếp tục vòng lặp chính")
                                            else:
                                                print("⚠️ Vòng lặp gói không thành công, nhưng vẫn tiếp tục vòng lặp chính")

                                # Đợi sau khi xử lý buy.png (tăng thời gian chờ)
                                if self._random_sleep(2.0, 3.0, "Chờ sau khi xử lý buy.png", check_stop_flag):
                                    return False
                            break
                        else:
                            print(f"❌ Không tìm thấy buy.png hiện tại trong lần thử {attempt + 1}")
                            if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                                if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                    print("⚠️ Dừng trong khi chờ thử lại")
                                    return False

                # Nếu không tìm thấy buy.png sau khi tap vào vị trí thẻ, thoát về màn hình chính ngay lập tức
                # NHƯNG không thoát nếu đã bỏ qua buy.png do buy_pass
                print(f"🔍 DEBUG: buy_found = {buy_found}, iteration = {iteration}, skip_buy_due_to_pass = {skip_buy_due_to_pass}")
                if not buy_found and not skip_buy_due_to_pass:
                    print(f"🚪 ĐIỀU KIỆN THOÁT: Không tìm thấy buy.png hiện tại trong vòng lặp {iteration}")
                    print("🏠 Thoát về màn hình chính ngay lập tức (có thể đã hết thẻ hoặc không có thẻ tại vị trí này)")
                    print("🏠 ĐANG NHẤN PHÍM HOME")
                    print("🚫 SẼ BỎ QUA VÒNG LẶP PHỤ (KHÔNG VUỐT LÊN)")
                    self.press_key(3)  # KEYCODE_HOME
                    print("🔄 TRẠNG THÁI: Trả về 'no_more_cards' để bỏ qua vòng lặp phụ")
                    return "no_more_cards"  # Trả về tín hiệu đặc biệt
                elif buy_found:
                    print("✅ Đã tìm thấy buy.png, tiếp tục vòng lặp")
                elif skip_buy_due_to_pass:
                    print("ℹ️ Bỏ qua kiểm tra thoát vì đã bỏ qua buy.png do buy_pass, tiếp tục vòng lặp")

                # Tìm và tap vào see_option.png chỉ ở vòng lặp đầu tiên
                if iteration == 1:
                    print(f"🔍 Tìm see_option.png trong vòng lặp đầu tiên")
                    see_option_template_path = os.path.join(template_dir, "see_option.png")

                    if os.path.exists(see_option_template_path):
                        see_option_found = False
                        for attempt in range(8):  # Thử tối đa 8 lần
                            if should_stop():
                                print(f"⚠️ Dừng trong lần thử {attempt + 1}/2 tìm see_option.png trong vòng lặp đầu tiên")
                                return False

                            print(f"Lần thử {attempt + 1}/2 tìm see_option.png trong vòng lặp đầu tiên")
                            if self.find_image_and_tap(see_option_template_path, threshold=0.6, check_stop_flag=check_stop_flag):
                                see_option_found = True
                                print(f"✅ Đã tìm thấy và tap vào see_option.png trong vòng lặp đầu tiên")
                                if self._random_sleep(2.0, 3.0, f"Chờ sau khi tap see_option.png trong vòng lặp đầu tiên", check_stop_flag):
                                    return False
                                break
                            else:
                                print(f"❌ Không tìm thấy see_option.png trong lần thử {attempt + 1} của vòng lặp đầu tiên")
                                if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                                    if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                        return False

                        if see_option_found:
                            print(f"✅ Đã xử lý see_option.png trong vòng lặp đầu tiên thành công")
                        else:
                            print(f"ℹ️ Không tìm thấy see_option.png trong vòng lặp đầu tiên sau 2 lần thử")
                    else:
                        print(f"⚠️ Không tìm thấy file template see_option.png")
                else:
                    print(f"ℹ️ Bỏ qua kiểm tra see_option.png ở vòng lặp {iteration} (chỉ kiểm tra ở vòng lặp đầu tiên)")

                # Bước cuối: Tap vào tọa độ để chuẩn bị cho vòng lặp tiếp theo
                print(f"👆 Bước cuối vòng lặp {iteration}: Tap vào tọa độ thẻ thứ {self.current_card_number}")
                print(f"👆 Tap vào tọa độ ({x}, {y}) (thẻ thứ {self.current_card_number})")
                try:
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(x), str(y)],
                        check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    print(f"✅ Đã tap vào tọa độ thẻ thứ {self.current_card_number} ({x}, {y}) ở cuối vòng lặp {iteration}")

                    # Cập nhật status sau khi tap vào thẻ
                    try:
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass

                    if self._random_sleep(5.0, 6.0, f"Chờ sau khi tap tọa độ thẻ thứ {self.current_card_number}", check_stop_flag):
                        return False
                except Exception as e:
                    print(f"❌ Lỗi khi tap vào tọa độ thẻ thứ {self.current_card_number} ({x}, {y}) ở cuối vòng lặp {iteration}: {str(e)}")

            print("✅ ĐÃ HOÀN THÀNH 8 VÒNG LẶP CHÍNH - VẪN CÒN THẺ")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện vòng lặp chính: {str(e)}")
            return False

    def execute_auxiliary_loops(self, check_stop_flag=None):
        """Thực hiện 15 vòng lặp phụ để xử lý các thẻ ẩn phía dưới"""
        try:
            def should_stop():
                return check_stop_flag and callable(check_stop_flag) and check_stop_flag()

            # Lấy đường dẫn template
            template_dir = path_manager.get_template_path()
            buy_template_path = os.path.join(template_dir, "buy.png")
            success_template_path = os.path.join(template_dir, "success.png")

            print("🔄 Bắt đầu 15 vòng lặp phụ để xử lý các thẻ ẩn phía dưới")

            for hidden_iteration in range(1, 16):  # 15 vòng lặp phụ
                # Cập nhật thứ tự thẻ (vòng lặp phụ bắt đầu từ thẻ thứ 10)
                self.current_card_number = 9 + hidden_iteration

                if should_stop():
                    print(f"⛔ Đã nhận tín hiệu dừng tại vòng lặp phụ {hidden_iteration}")
                    return False

                print(f"🔄 Vòng lặp phụ {hidden_iteration}/15: Xử lý thẻ ẩn (thẻ thứ {self.current_card_number})")

                # 1. Bước đầu tiên: Tìm và tap buy.png (vì vòng lặp chính đã tap vào vị trí thẻ cuối cùng)
                print(f"🔍 1. Tìm buy.png trong vòng lặp phụ {hidden_iteration}/15 (bước đầu tiên)")
                buy_found_in_aux = False
                for attempt in range(3):  # Thử tối đa 3 lần
                    if should_stop():
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm buy.png trong vòng lặp phụ {hidden_iteration}")
                    found, _ = self.find_image_on_screen(buy_template_path, check_stop_flag=check_stop_flag)
                    if found:
                        buy_found_in_aux = True
                        print(f"✅ Đã tìm thấy buy.png trong vòng lặp phụ {hidden_iteration}")

                        # Tap vào buy.png
                        if self.find_image_and_tap(buy_template_path, check_stop_flag=check_stop_flag):
                            print(f"✅ Đã tap vào buy.png trong vòng lặp phụ {hidden_iteration}")

                            # Đợi 4 giây trước khi kiểm tra success.png (vì success xuất hiện sau 4s)
                            print("⏳ Đợi 4 giây để success.png xuất hiện...")
                            if self._random_sleep(4.0, 4.5, "Chờ success.png xuất hiện sau khi tap buy", check_stop_flag):
                                return False

                            # Kiểm tra success.png liên tục trong 8 giây (vì success chỉ hiển thị 3s)
                            success_found = False
                            if os.path.exists(success_template_path):
                                print("🔍 Kiểm tra success.png liên tục trong 8 giây...")
                                check_start = time.time()
                                check_duration = 8.0  # Kiểm tra trong 8 giây

                                while time.time() - check_start < check_duration:
                                    if check_stop_flag and callable(check_stop_flag) and check_stop_flag():
                                        return False

                                    found, _ = self.find_image_on_screen(success_template_path, threshold=0.6, check_stop_flag=check_stop_flag)
                                    if found:
                                        success_found = True
                                        elapsed_time = time.time() - check_start
                                        print(f"✅ Phát hiện success.png sau {elapsed_time:.2f} giây trong vòng lặp phụ - Thực hiện vòng lặp gói")
                                        package_result = self.execute_package_loops(check_stop_flag)
                                        if package_result:
                                            print("✅ Hoàn thành vòng lặp gói trong vòng lặp phụ, tiếp tục")
                                        else:
                                            print("⚠️ Vòng lặp gói không thành công trong vòng lặp phụ, nhưng vẫn tiếp tục")
                                        break

                                    # Đợi ngắn trước khi kiểm tra lại
                                    time.sleep(0.2)

                                if not success_found:
                                    total_time = time.time() - check_start
                                    print(f"ℹ️ Không tìm thấy success.png sau {total_time:.2f} giây kiểm tra liên tục trong vòng lặp phụ")

                            # Nếu không tìm thấy success.png, kiểm tra tap_join.png
                            if not success_found:
                                tap_join_template_path = os.path.join(template_dir, "tap_join.png")
                                if os.path.exists(tap_join_template_path):
                                    tap_join_found, _ = self.find_image_on_screen(tap_join_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                                    if tap_join_found:
                                        print("✅ Phát hiện tap_join.png trong vòng lặp phụ - Tap và thực hiện vòng lặp gói")
                                        if self.find_image_and_tap(tap_join_template_path, threshold=0.7, check_stop_flag=check_stop_flag):
                                            # Đợi và tìm got_it.png
                                            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap tap_join.png trong vòng lặp phụ", check_stop_flag):
                                                return False

                                            got_it_template_path = os.path.join(template_dir, "got_it.png")
                                            if os.path.exists(got_it_template_path):
                                                if self.find_image_and_tap(got_it_template_path, check_stop_flag=check_stop_flag):
                                                    print("✅ Đã tap vào got_it.png trong vòng lặp phụ")

                                            # Thực hiện vòng lặp gói
                                            package_result = self.execute_package_loops(check_stop_flag)
                                            if package_result:
                                                print("✅ Hoàn thành vòng lặp gói trong vòng lặp phụ, tiếp tục")
                                            else:
                                                print("⚠️ Vòng lặp gói không thành công trong vòng lặp phụ, nhưng vẫn tiếp tục")
                        break
                    else:
                        print(f"❌ Không tìm thấy buy.png trong lần thử {attempt + 1} của vòng lặp phụ {hidden_iteration}")
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                            if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2} trong vòng lặp phụ", check_stop_flag):
                                return False

                if not buy_found_in_aux:
                    print(f"🚪 Không tìm thấy buy.png trong vòng lặp phụ {hidden_iteration}/15 - có thể đã hết thẻ ẩn")
                    print("🏠 Thoát về màn hình chính ngay lập tức (đã hết thẻ ẩn)")
                    print("🔄 Kết thúc vòng lặp phụ sớm do không còn thẻ")

                    # Không gọi exit_app_completely ở đây để tránh tắt app 2 lần
                    # Sẽ được gọi ở perform_action_sequence() sau khi return
                    print("✅ Hoàn thành vòng lặp phụ - sẽ thoát app ở perform_action_sequence")

                    return "no_more_hidden_cards"  # Trả về signal đặc biệt

                # Logic vuốt: Lần đầu 300px, sau đó mỗi lần thêm 140px
                if hidden_iteration == 1:
                    # Lần đầu tiên: vuốt 300px
                    swipe_distance = 300
                    print(f"📱 Lần {hidden_iteration}/15: Vuốt lên {swipe_distance}px (lần đầu)")
                else:
                    # Các lần sau: 300 + (iteration-1) * 135
                    swipe_distance = 300 + (hidden_iteration - 1) * 135
                    print(f"📱 Lần {hidden_iteration}/15: Vuốt lên {swipe_distance}px (300 + {hidden_iteration-1} × 135)")

                try:
                    start_x = self.screen_width // 2
                    start_y = self.screen_height // 2 + 200
                    end_x = self.screen_width // 2
                    end_y = start_y - swipe_distance

                    print(f"📱 Vuốt từ ({start_x}, {start_y}) đến ({end_x}, {end_y}) - khoảng cách {swipe_distance}px")
                    subprocess.run([
                        ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                        str(start_x), str(start_y), str(end_x), str(end_y), "500"
                    ], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi sau khi vuốt để giao diện ổn định
                    if self._random_sleep(2.0, 3.0, f"Chờ sau khi vuốt {swipe_distance}px", check_stop_flag):
                        return False

                except Exception as e:
                    print(f"❌ Lỗi khi vuốt {swipe_distance}px trong iteration {hidden_iteration}: {str(e)}")
                    continue

                # Tap vào vị trí cố định (368, 1863) để không sót thẻ
                tap_x, tap_y = 368, 1863
                print(f"👆 Tap vào vị trí cố định thẻ thứ {self.current_card_number} ({tap_x}, {tap_y}) trong vòng lặp phụ {hidden_iteration}/9")

                try:
                    subprocess.run([
                        ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(tap_x), str(tap_y)
                    ], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    print(f"✅ Đã tap vào thẻ thứ {self.current_card_number} ({tap_x}, {tap_y}) trong vòng lặp phụ {hidden_iteration}")

                    # Cập nhật status sau khi tap vào thẻ ẩn
                    try:
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass

                    # Đợi lâu hơn sau khi tap để buy.png kịp xuất hiện
                    if self._random_sleep(3.0, 4.0, f"Chờ sau khi tap thẻ thứ {self.current_card_number} vòng lặp phụ {hidden_iteration}", check_stop_flag):
                        return False

                except Exception as e:
                    print(f"❌ Lỗi khi tap vào thẻ thứ {self.current_card_number} ({tap_x}, {tap_y}) lần {hidden_iteration}: {str(e)}")
                    continue



            print("✅ Đã hoàn thành 9 vòng lặp phụ xử lý thẻ ẩn")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện vòng lặp phụ: {str(e)}")
            return False

    def execute_package_loops(self, check_stop_flag=None):
        """Thực hiện các vòng lặp gói (1, 2, 3) - chỉ thoát khi xuất hiện see_option.png"""
        try:
            # Phát âm thanh meo.wav khi vào vòng lặp gói
            print("🔊 Vào vòng lặp gói - Phát âm thanh meo.wav")
            self.play_sound("meo.wav")

            def should_stop():
                return check_stop_flag and callable(check_stop_flag) and check_stop_flag()

            print("🎯 Bắt đầu thực hiện vòng lặp gói")

            template_dir = path_manager.get_template_path()
            buy_template_path = os.path.join(template_dir, "buy.png")
            see_option_template_path = os.path.join(template_dir, "see_option.png")

            # Biến để điều khiển thoát khỏi tất cả vòng lặp gói
            exit_all_package_loops = False

            # Thực hiện vòng lặp gói 1
            if self.goi1_value > 0 and not exit_all_package_loops:
                self.current_package = 1
                self.package_count = 0
                print(f"🎯 Bắt đầu vòng lặp gói 1 ({self.goi1_value} lần)")

                # Cập nhật trạng thái khi bắt đầu gói 1
                try:
                    # Gọi hàm cập nhật trạng thái nếu có
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                        self.update_status_func()
                except:
                    pass

                for i in range(self.goi1_value):
                    if should_stop() or exit_all_package_loops:
                        print(f"⚠️ Dừng trong vòng lặp gói 1 lần {i+1}/{self.goi1_value}")
                        break

                    # Cập nhật số lần mua gói hiện tại
                    self.package_count = i + 1

                    # Cập nhật trạng thái ngay sau khi cập nhật package_count
                    try:
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass

                    print(f"➡️ Gói 1 - Lần {self.package_count}/{self.goi1_value}: Tìm và tap goi1.png")
                    package_tapped = self.find_and_tap_package(
                        self.goi1_template_path,
                        "Gói 1",
                        self.package_count,
                        self.goi1_value,
                        check_stop_flag
                    )

                    if not package_tapped:
                        print(f"❌ Không thể tìm thấy goi1.png trong lần {self.package_count}/{self.goi1_value}")
                        continue

                    if self._random_sleep(2.0, 3.0, f"Chờ sau khi tap vào gói 1 lần {i+1}", check_stop_flag):
                        print(f"⚠️ Dừng trong khi chờ sau khi tap gói 1")
                        break

                    # Tìm và tap vào buy.png sau mỗi lần tap vào gói
                    buy_found = False
                    for attempt in range(3):
                        if should_stop():
                            print(f"⚠️ Dừng trong lần thử {attempt + 1}/3 tìm buy.png trong vòng lặp gói 1")
                            break

                        print(f"Lần thử {attempt + 1}/3 tìm buy.png trong vòng lặp gói 1")
                        if self.find_image_and_tap(buy_template_path, check_stop_flag=check_stop_flag):
                            buy_found = True
                            print("✅ Đã tìm thấy và tap vào buy.png trong vòng lặp gói 1")

                            # Kiểm tra và tap vào ok_mua.png nếu có xuất hiện
                            self.check_and_tap_ok_mua(check_stop_flag, "trong vòng lặp gói 1")

                            # Đợi sau khi tap buy.png
                            if self._random_sleep(4.0, 6.0, "Chờ sau khi tap buy.png trong vòng lặp gói 1", check_stop_flag):
                                break

                            # Kiểm tra see_option.png - nếu có thì tap và thoát tất cả vòng lặp gói (thử 2 lần)
                            see_option_found = False
                            for see_attempt in range(2):  # Thử tối đa 2 lần
                                if should_stop():
                                    break

                                print(f"🔍 Lần thử {see_attempt + 1}/2 tìm see_option.png trong vòng lặp gói 1 lần {i+1}")
                                if self.find_image_and_tap(see_option_template_path, threshold=0.7, check_stop_flag=check_stop_flag):
                                    see_option_found = True
                                    print(f"✅ Đã tìm thấy và tap vào see_option.png trong vòng lặp gói 1 lần {i+1} (lần thử {see_attempt + 1})")
                                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap see_option.png", check_stop_flag):
                                        break
                                    print(f"⚠️ Thoát khỏi tất cả các vòng lặp gói và quay lại vòng lặp chính")
                                    exit_all_package_loops = True
                                    break  # Thoát khỏi vòng lặp gói 1
                                else:
                                    print(f"❌ Không tìm thấy see_option.png trong lần thử {see_attempt + 1}/2 của vòng lặp gói 1 lần {i+1}")
                                    if see_attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {see_attempt + 2}", check_stop_flag):
                                            break

                            if not see_option_found:
                                print(f"ℹ️ Không tìm thấy see_option.png trong vòng lặp gói 1 lần {i+1} sau 2 lần thử")
                            break

                    if not buy_found:
                        print(f"❌ Không tìm thấy buy.png trong vòng lặp gói 1 lần {i+1}")

                    # Nếu đã tìm thấy see_option.png thì thoát
                    if exit_all_package_loops:
                        break

            # Thực hiện vòng lặp gói 2
            if self.goi2_value > 0 and not exit_all_package_loops:
                self.current_package = 2
                self.package_count = 0
                print(f"🎯 Bắt đầu vòng lặp gói 2 ({self.goi2_value} lần)")

                # Cập nhật trạng thái khi bắt đầu gói 2
                try:
                    # Gọi hàm cập nhật trạng thái nếu có
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                        self.update_status_func()
                except:
                    pass

                for i in range(self.goi2_value):
                    if should_stop() or exit_all_package_loops:
                        print(f"⚠️ Dừng trong vòng lặp gói 2 lần {i+1}/{self.goi2_value}")
                        break

                    # Cập nhật số lần mua gói hiện tại
                    self.package_count = i + 1

                    # Cập nhật trạng thái ngay sau khi cập nhật package_count
                    try:
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass

                    print(f"➡️ Gói 2 - Lần {self.package_count}/{self.goi2_value}: Tìm và tap goi2.png")
                    package_tapped = self.find_and_tap_package(
                        self.goi2_template_path,
                        "Gói 2",
                        self.package_count,
                        self.goi2_value,
                        check_stop_flag
                    )

                    if not package_tapped:
                        print(f"❌ Không thể tìm thấy goi2.png trong lần {self.package_count}/{self.goi2_value}")
                        continue

                    if self._random_sleep(2.0, 3.0, f"Chờ sau khi tap vào gói 2 lần {i+1}", check_stop_flag):
                        print(f"⚠️ Dừng trong khi chờ sau khi tap gói 2")
                        break

                    # Tìm và tap vào buy.png sau mỗi lần tap vào gói
                    buy_found = False
                    for attempt in range(3):
                        if should_stop():
                            print(f"⚠️ Dừng trong lần thử {attempt + 1}/3 tìm buy.png trong vòng lặp gói 2")
                            break

                        print(f"Lần thử {attempt + 1}/3 tìm buy.png trong vòng lặp gói 2")
                        if self.find_image_and_tap(buy_template_path, check_stop_flag=check_stop_flag):
                            buy_found = True
                            print("✅ Đã tìm thấy và tap vào buy.png trong vòng lặp gói 2")

                            # Kiểm tra và tap vào ok_mua.png nếu có xuất hiện
                            self.check_and_tap_ok_mua(check_stop_flag, "trong vòng lặp gói 2")

                            # Đợi sau khi tap buy.png
                            if self._random_sleep(4.0, 6.0, "Chờ sau khi tap buy.png trong vòng lặp gói 2", check_stop_flag):
                                break

                            # Kiểm tra see_option.png - nếu có thì tap và thoát tất cả vòng lặp gói (thử 2 lần)
                            see_option_found = False
                            for see_attempt in range(2):  # Thử tối đa 2 lần
                                if should_stop():
                                    break

                                print(f"🔍 Lần thử {see_attempt + 1}/2 tìm see_option.png trong vòng lặp gói 2 lần {i+1}")
                                if self.find_image_and_tap(see_option_template_path, threshold=0.6, check_stop_flag=check_stop_flag):
                                    see_option_found = True
                                    print(f"✅ Đã tìm thấy và tap vào see_option.png trong vòng lặp gói 2 lần {i+1} (lần thử {see_attempt + 1})")
                                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap see_option.png", check_stop_flag):
                                        break
                                    print(f"⚠️ Thoát khỏi tất cả các vòng lặp gói và quay lại vòng lặp chính")
                                    exit_all_package_loops = True
                                    break  # Thoát khỏi vòng lặp gói 2
                                else:
                                    print(f"❌ Không tìm thấy see_option.png trong lần thử {see_attempt + 1}/2 của vòng lặp gói 2 lần {i+1}")
                                    if see_attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {see_attempt + 2}", check_stop_flag):
                                            break

                            if not see_option_found:
                                print(f"ℹ️ Không tìm thấy see_option.png trong vòng lặp gói 2 lần {i+1} sau 2 lần thử")
                            break

                    if not buy_found:
                        print(f"❌ Không tìm thấy buy.png trong vòng lặp gói 2 lần {i+1}")

                    # Nếu đã tìm thấy see_option.png thì thoát
                    if exit_all_package_loops:
                        break

            # Thực hiện vòng lặp gói 3
            if self.goi3_value > 0 and not exit_all_package_loops:
                self.current_package = 3
                self.package_count = 0
                print(f"🎯 Bắt đầu vòng lặp gói 3 ({self.goi3_value} lần)")

                # Cập nhật trạng thái khi bắt đầu gói 3
                try:
                    # Gọi hàm cập nhật trạng thái nếu có
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                        self.update_status_func()
                except:
                    pass

                for i in range(self.goi3_value):
                    if should_stop() or exit_all_package_loops:
                        print(f"⚠️ Dừng trong vòng lặp gói 3 lần {i+1}/{self.goi3_value}")
                        break

                    # Cập nhật số lần mua gói hiện tại
                    self.package_count = i + 1

                    # Cập nhật trạng thái ngay sau khi cập nhật package_count
                    try:
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass

                    print(f"➡️ Gói 3 - Lần {self.package_count}/{self.goi3_value}: Tìm và tap goi3.png")
                    package_tapped = self.find_and_tap_package(
                        self.goi3_template_path,
                        "Gói 3",
                        self.package_count,
                        self.goi3_value,
                        check_stop_flag
                    )

                    if not package_tapped:
                        print(f"❌ Không thể tìm thấy goi3.png trong lần {self.package_count}/{self.goi3_value}")
                        continue

                    if self._random_sleep(2.0, 3.0, f"Chờ sau khi tap vào gói 3 lần {i+1}", check_stop_flag):
                        print(f"⚠️ Dừng trong khi chờ sau khi tap gói 3")
                        break

                    # Tìm và tap vào buy.png sau mỗi lần tap vào gói
                    buy_found = False
                    for attempt in range(3):
                        if should_stop():
                            print(f"⚠️ Dừng trong lần thử {attempt + 1}/3 tìm buy.png trong vòng lặp gói 3")
                            break

                        print(f"Lần thử {attempt + 1}/3 tìm buy.png trong vòng lặp gói 3")
                        if self.find_image_and_tap(buy_template_path, check_stop_flag=check_stop_flag):
                            buy_found = True
                            print("✅ Đã tìm thấy và tap vào buy.png trong vòng lặp gói 3")

                            # Kiểm tra và tap vào ok_mua.png nếu có xuất hiện
                            self.check_and_tap_ok_mua(check_stop_flag, "trong vòng lặp gói 3")

                            # Đợi sau khi tap buy.png
                            if self._random_sleep(5.0, 6.0, "Chờ sau khi tap buy.png trong vòng lặp gói 3", check_stop_flag):
                                break

                            # Kiểm tra see_option.png - nếu có thì tap và thoát tất cả vòng lặp gói (thử 2 lần)
                            see_option_found = False
                            for see_attempt in range(2):  # Thử tối đa 2 lần
                                if should_stop():
                                    break

                                print(f"🔍 Lần thử {see_attempt + 1}/2 tìm see_option.png trong vòng lặp gói 3 lần {i+1}")
                                if self.find_image_and_tap(see_option_template_path, threshold=0.6, check_stop_flag=check_stop_flag):
                                    see_option_found = True
                                    print(f"✅ Đã tìm thấy và tap vào see_option.png trong vòng lặp gói 3 lần {i+1} (lần thử {see_attempt + 1})")
                                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap see_option.png", check_stop_flag):
                                        break
                                    print(f"⚠️ Thoát khỏi tất cả các vòng lặp gói và quay lại vòng lặp chính")
                                    exit_all_package_loops = True
                                    break  # Thoát khỏi vòng lặp gói 3
                                else:
                                    print(f"❌ Không tìm thấy see_option.png trong lần thử {see_attempt + 1}/2 của vòng lặp gói 3 lần {i+1}")
                                    if see_attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {see_attempt + 2}", check_stop_flag):
                                            break

                            if not see_option_found:
                                print(f"ℹ️ Không tìm thấy see_option.png trong vòng lặp gói 3 lần {i+1} sau 2 lần thử")
                            break

                    if not buy_found:
                        print(f"❌ Không tìm thấy buy.png trong vòng lặp gói 3 lần {i+1}")

                    # Nếu đã tìm thấy see_option.png thì thoát
                    if exit_all_package_loops:
                        break

            if exit_all_package_loops:
                print("✅ Đã thoát khỏi vòng lặp gói do phát hiện see_option.png")
            else:
                print("✅ Đã hoàn thành tất cả vòng lặp gói")

            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện vòng lặp gói: {str(e)}")
            return False