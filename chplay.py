import sys
import subprocess
import random
import time
import os
import cv2
import numpy as np
import threading
import concurrent.futures
import queue
from PyQt5 import QtWidgets
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, QCoreApplication, QEvent
from PyQt5.QtWidgets import QApplication

# Import path_manager để quản lý đường dẫn
import path_manager

# Biến toàn cục để lưu đường dẫn ADB
ADB_PATH = "adb"  # Giá trị mặc định


class ChPlayLogin:
    """
    Thực hiện các hành động tự động đăng nhập bằng nút Google Play.
    """

    def __init__(self, serial, num_loops=1, device_index=0, total_devices=1, adb_path=None, password=None, auth_secret=None, data=None):
        self.serial = serial
        # Số lần lặp lại quy trình
        self.num_loops = num_loops
        # Biến đếm số lần lặp hiện tại
        self.loop_count = 0
        # Kích thước màn hình mặc định
        self.screen_width = 1080
        self.screen_height = 1920
        # Chỉ số thiết bị
        self.device_index = device_index
        # Tổng số thiết bị
        self.total_devices = total_devices
        # Mật khẩu từ giao diện (nếu cần)
        self.password = password
        # Mã xác thực từ giao diện (nếu có)
        self.auth_secret = auth_secret
        # Data từ cột số 4 (ưu tiên cao nhất cho email kháng)
        self.data = data

        # Hàm callback để cập nhật trạng thái (sẽ được set từ AutoK.py)
        self.update_status_func = None

        # Biến trạng thái để theo dõi bước hiện tại (như mua.py)
        self.current_step = ""
        self.current_progress = ""

        # Biến countdown cho captcha
        self.captcha_countdown = 0

        # Bỏ debug để tăng tốc

        # Lấy đường dẫn đến thư mục template
        template_dir = path_manager.get_template_path()
        self.chplay_template_path = os.path.join(template_dir, "chplay.png")
        self.next_template_path = os.path.join(template_dir, "next.png")
        self.not_robot_template_path = os.path.join(template_dir, "not_robot.png")
        self.welcome_template_path = os.path.join(template_dir, "welcom.png")
        self.enter_pass_template_path = os.path.join(template_dir, "enter_your_pass.png")
        self.authen_template_path = os.path.join(template_dir, "authen.png")
        self.get_start_template_path = os.path.join(template_dir, "get_start.png")
        self.use_pass_template_path = os.path.join(template_dir, "use_pass.png")

        # Cập nhật biến toàn cục ADB_PATH nếu được cung cấp
        global ADB_PATH
        if adb_path:
            ADB_PATH = adb_path
            print(f"✅ Đã cập nhật đường dẫn ADB: {ADB_PATH}")

        # Lấy kích thước màn hình thực tế của thiết bị
        self.get_device_screen_size()

    def get_device_screen_size(self):
        """
        Lấy kích thước màn hình thực tế của thiết bị.
        """
        try:
            # Sử dụng dumpsys window để lấy kích thước màn hình
            result = subprocess.run(
                [ADB_PATH, "-s", self.serial, "shell", "dumpsys", "window", "displays"],
                capture_output=True, text=True, check=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            # Tìm kiếm thông tin kích thước màn hình trong output
            output = result.stdout
            # Tìm kiếm các mẫu như "init=1080x1920" hoặc "cur=1080x1920"
            import re
            size_match = re.search(r'init=(\d+)x(\d+)', output) or re.search(r'cur=(\d+)x(\d+)', output)

            if size_match:
                self.screen_width = int(size_match.group(1))
                self.screen_height = int(size_match.group(2))
        except Exception as e:
            pass  # Sử dụng kích thước mặc định

    def update_status(self, message):
        """
        Cập nhật trạng thái hiện tại.
        :param message: Thông báo trạng thái
        """
        # Nếu có hàm cập nhật trạng thái từ giao diện chính, gọi nó
        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
            try:
                self.update_status_func()
            except Exception as e:
                pass

    def log_to_ui(self, message):
        """
        Gửi thông báo đến UI - LOẠI BỎ, dùng cách của mua.py thay thế.
        """
        # Chỉ in ra console như mua.py
        print(f"📢 {message}")



    def _random_sleep(self, min_sec=0.5, max_sec=1.0, action_desc="", check_stop_flag=None):
        """
        Tạm dừng thực thi trong khoảng thời gian ngẫu nhiên.
        :param min_sec: Thời gian tối thiểu (giây)
        :param max_sec: Thời gian tối đa (giây)
        :param action_desc: Mô tả hành động đang thực hiện
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu đã dừng do cờ dừng, False nếu đã ngủ hết thời gian
        """
        # Điều chỉnh thời gian chờ để cân bằng giữa tốc độ và độ chính xác
        t = random.uniform(min_sec, max_sec)

        # Nếu có hàm kiểm tra cờ dừng, kiểm tra mỗi 0.05 giây
        if check_stop_flag and callable(check_stop_flag):
            start_time = time.time()
            end_time = start_time + t

            while time.time() < end_time:
                # Kiểm tra cờ dừng
                if check_stop_flag():
                    return True  # Đã dừng do cờ dừng

                # Ngủ một khoảng thời gian ngắn hơn để phản ứng nhanh hơn với cờ dừng
                time.sleep(0.05)

            return False  # Đã ngủ hết thời gian
        else:
            # Nếu không có hàm kiểm tra cờ dừng, ngủ bình thường
            time.sleep(t)
            return False

    def tap_at_position(self, x, y, description=""):
        """
        Tap vào vị trí cụ thể trên màn hình.
        :param x: Tọa độ x
        :param y: Tọa độ y
        :param description: Mô tả hành động (để ghi log)
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            subprocess.run(
                [ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(x), str(y)],
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )
            return True
        except Exception as e:
            return False

    def press_key(self, keycode):
        """
        Gửi phím keyevent đến thiết bị.
        :param keycode: Mã phím (keycode) cần gửi
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", str(keycode)],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return True
        except Exception as e:
            return False

    def input_text(self, text):
        """
        Nhập văn bản vào thiết bị với xử lý khoảng trắng và ký tự đặc biệt.
        :param text: Văn bản cần nhập
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"🔤 Đang nhập văn bản (độ dài: {len(text)} ký tự)")

            # Xử lý ký tự đặc biệt và khoảng trắng
            safe_text = text

            # Thay thế xuống dòng bằng khoảng trắng
            safe_text = safe_text.replace('\n', ' ').replace('\r', ' ')

            # Thay thế các ký tự đặc biệt có thể gây lỗi shell
            safe_text = safe_text.replace('"', ' ').replace("'", ' ').replace('\\', ' ')
            safe_text = safe_text.replace('`', ' ').replace('$', ' ').replace('&', ' ')
            safe_text = safe_text.replace('|', ' ').replace(';', ' ').replace('(', ' ')
            safe_text = safe_text.replace(')', ' ').replace('<', ' ').replace('>', ' ')

            # Loại bỏ khoảng trắng thừa
            safe_text = ' '.join(safe_text.split())

            print(f"📝 Văn bản sau khi xử lý (độ dài: {len(safe_text)} ký tự): {safe_text[:100]}{'...' if len(safe_text) > 100 else ''}")

            # Sử dụng quotes để bao bọc text
            quoted_text = f'"{safe_text}"'

            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", quoted_text],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            print(f"✅ Đã nhập văn bản thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi nhập văn bản: {str(e)}")

            # Thử phương pháp fallback - nhập từng từ
            try:
                print(f"🔄 Thử phương pháp fallback - nhập từng phần...")

                # Chia text thành tất cả các từ (không giới hạn)
                words = safe_text.split()  # Lấy tất cả từ

                for i, word in enumerate(words):
                    # Hiển thị progress mỗi 10 từ để không spam log
                    if i % 10 == 0 or i == len(words) - 1:
                        print(f"📝 Đang nhập từ {i+1}/{len(words)}: {word}")

                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", word],
                                  check=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Thêm khoảng trắng sau mỗi từ (trừ từ cuối)
                    if i < len(words) - 1:
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", " "],
                                      check=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi một chút giữa các từ (giảm thời gian để nhanh hơn)
                    time.sleep(0.1)

                print(f"✅ Đã nhập văn bản thành công (phương pháp fallback)")
                return True

            except Exception as e2:
                print(f"❌ Phương pháp fallback cũng thất bại: {str(e2)}")
                return False

    def _find_and_tap_next_khang_after_text(self):
        """
        Tìm và tap next_khang sau khi nhập text xong
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🔍 Tìm và tap next_khang sau khi nhập text...")

            import path_manager
            next_khang_template_path = os.path.join(path_manager.get_template_path(), "next_khang.png")

            if not os.path.exists(next_khang_template_path):
                print(f"❌ Không tìm thấy file template next_khang.png tại {next_khang_template_path}")
                return False

            # Đợi một chút sau khi nhập text
            time.sleep(2.0)

            # Thử tìm next_khang.png tối đa 5 lần
            for attempt in range(5):
                print(f"Lần thử {attempt + 1}/5 tìm next_khang.png")

                if self.find_image_and_tap(next_khang_template_path):
                    print("✅ Đã tìm thấy và tap vào next_khang.png")

                    # Đợi một chút sau khi tap next_khang
                    time.sleep(2.0)

                    # Tiếp tục với các bước cuối
                    return self._complete_appeal_final_steps()
                else:
                    print(f"❌ Không tìm thấy next_khang.png trong lần thử {attempt + 1}")
                    if attempt < 4:
                        time.sleep(2.0)

            print("❌ Không thể tìm thấy next_khang.png sau 5 lần thử")
            return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm next_khang sau khi nhập text: {str(e)}")
            return False

    def input_text_with_next_khang(self, text):
        """
        Nhập text và sau đó tìm tap next_khang (dành riêng cho kháng email)
        :param text: Văn bản cần nhập
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Nhập text bình thường
            if self.input_text(text):
                print(f"✅ Đã nhập nội dung kháng email thành công")

                # Sau khi nhập text xong, tìm và tap next_khang
                return self._find_and_tap_next_khang_after_text()
            else:
                print(f"❌ Không thể nhập nội dung kháng email")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi nhập nội dung kháng email với next_khang: {str(e)}")
            return False

    def _complete_appeal_final_steps(self):
        """
        Hoàn thành các bước cuối của quy trình kháng email:
        1. Tìm và tap enter_contact.png
        2. Nhập email random từ file emailkp.txt
        3. Tìm và tap submit_appeal.png
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🔍 Bắt đầu các bước cuối của quy trình kháng email...")

            import path_manager

            # Bước 1: Tìm và tap enter_contact.png
            print("🔍 Bước 1: Tìm và tap enter_contact.png")
            enter_contact_template_path = os.path.join(path_manager.get_template_path(), "enter_contact.png")

            if not os.path.exists(enter_contact_template_path):
                print(f"❌ Không tìm thấy file template enter_contact.png tại {enter_contact_template_path}")
                return False

            # Thử tìm enter_contact.png tối đa 5 lần
            enter_contact_found = False
            for attempt in range(5):
                print(f"Lần thử {attempt + 1}/5 tìm enter_contact.png")

                if self.find_image_and_tap(enter_contact_template_path):
                    enter_contact_found = True
                    print("✅ Đã tìm thấy và tap vào enter_contact.png")
                    break
                else:
                    print(f"❌ Không tìm thấy enter_contact.png trong lần thử {attempt + 1}")
                    if attempt < 4:
                        time.sleep(2.0)

            if not enter_contact_found:
                print("❌ Không thể tìm thấy enter_contact.png sau 5 lần thử")
                return False

            # Đợi một chút sau khi tap enter_contact
            time.sleep(2.0)

            # Bước 2: Nhập email random từ file emailkp.txt
            print("📧 Bước 2: Nhập email random từ file emailkp.txt")
            random_email = self._get_random_email_from_emailkp()

            if not random_email:
                print("❌ Không thể lấy email random từ file emailkp.txt")
                return False

            print(f"📧 Đã chọn email random: {random_email}")

            # Nhập email random
            if not self.input_text(random_email):
                print("❌ Không thể nhập email random")
                return False

            print("✅ Đã nhập email random thành công")

            # Đợi một chút sau khi nhập email
            time.sleep(2.0)

            # Bước 3: Tìm và tap submit_appeal.png
            print("🔍 Bước 3: Tìm và tap submit_appeal.png")
            submit_appeal_template_path = os.path.join(path_manager.get_template_path(), "submit_appeal.png")

            if not os.path.exists(submit_appeal_template_path):
                print(f"❌ Không tìm thấy file template submit_appeal.png tại {submit_appeal_template_path}")
                return False

            # Thử tìm submit_appeal.png tối đa 5 lần
            submit_appeal_found = False
            for attempt in range(5):
                print(f"Lần thử {attempt + 1}/5 tìm submit_appeal.png")

                if self.find_image_and_tap(submit_appeal_template_path):
                    submit_appeal_found = True
                    print("✅ Đã tìm thấy và tap vào submit_appeal.png")
                    break
                else:
                    print(f"❌ Không tìm thấy submit_appeal.png trong lần thử {attempt + 1}")
                    if attempt < 4:
                        time.sleep(2.0)

            if not submit_appeal_found:
                print("❌ Không thể tìm thấy submit_appeal.png sau 5 lần thử")
                return False

            print("🎉 Đã hoàn thành tất cả các bước kháng email!")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện các bước cuối của kháng email: {str(e)}")
            return False

    def _get_random_email_from_emailkp(self):
        """
        Lấy email random từ file emailkp.txt trong thư mục data
        :return: Email string hoặc None
        """
        try:
            import path_manager
            import random

            # Lấy đường dẫn đến file emailkp.txt trong thư mục data
            emailkp_file_path = path_manager.get_data_path("emailkp.txt")

            if not os.path.exists(emailkp_file_path):
                print(f"❌ Không tìm thấy file emailkp.txt tại {emailkp_file_path}")
                return None

            # Đọc file emailkp.txt
            with open(emailkp_file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # Lọc các dòng hợp lệ (có chứa @)
            valid_emails = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith("#") and "@" in line:
                    valid_emails.append(line)

            if not valid_emails:
                print("❌ Không có email hợp lệ nào trong file emailkp.txt")
                return None

            # Chọn email random
            random_email = random.choice(valid_emails)
            print(f"✅ Đã chọn email random từ {len(valid_emails)} email có sẵn: {random_email}")

            return random_email

        except Exception as e:
            print(f"❌ Lỗi khi đọc file emailkp.txt: {str(e)}")
            return None

    def load_password_from_mail_file(self):
        """
        Tải mật khẩu từ file mail.txt dựa trên chỉ số thiết bị.
        :return: Mật khẩu nếu tìm thấy, None nếu không tìm thấy
        """
        try:
            # Nếu đã có mật khẩu từ giao diện, sử dụng nó
            if self.password:
                return self.password

            # Lấy đường dẫn đến file mail.txt
            import path_manager
            mail_file_path = path_manager.get_txt_path("mail.txt")

            if not os.path.exists(mail_file_path):
                print(f"❌ Không tìm thấy file mail.txt tại {mail_file_path}")
                return None

            # Đọc file mail.txt
            with open(mail_file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # Lọc các dòng không phải comment và không trống
            valid_lines = [line.strip() for line in lines if line.strip() and not line.strip().startswith("#")]

            # Nếu không có dòng hợp lệ, trả về None
            if not valid_lines:
                print("❌ Không có dòng hợp lệ trong file mail.txt")
                return None

            # Tính toán chỉ số dòng dựa trên chỉ số thiết bị và tổng số thiết bị
            line_index = self.device_index % len(valid_lines)
            selected_line = valid_lines[line_index]

            # Phân tích dòng để lấy mật khẩu (định dạng: email|password|authentication)
            parts = selected_line.split("|")
            if len(parts) >= 2:
                password = parts[1].strip()
                print(f"✅ Đã tải mật khẩu từ file mail.txt (dòng {line_index + 1}): {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
                return password
            else:
                print(f"❌ Định dạng dòng không hợp lệ trong file mail.txt (dòng {line_index + 1}): {selected_line}")
                return None

        except Exception as e:
            print(f"❌ Lỗi khi tải mật khẩu từ file mail.txt: {str(e)}")
            return None

    def load_auth_secret_from_mail_file(self):
        """
        Tải mã xác thực (authentication secret) từ giao diện (ưu tiên) hoặc từ file mail.txt dựa trên chỉ số thiết bị.
        :return: Mã xác thực nếu tìm thấy, None nếu không tìm thấy
        """
        try:
            # Nếu đã có mã xác thực từ giao diện, sử dụng nó
            if self.auth_secret:
                print(f"✅ Sử dụng mã xác thực từ giao diện: {self.auth_secret[:4]}{'*' * (len(self.auth_secret) - 4) if len(self.auth_secret) > 4 else '*' * len(self.auth_secret)}")
                return self.auth_secret

            # Lấy đường dẫn đến file mail.txt
            import path_manager
            mail_file_path = path_manager.get_txt_path("mail.txt")

            if not os.path.exists(mail_file_path):
                print(f"❌ Không tìm thấy file mail.txt tại {mail_file_path}")
                return None

            # Đọc file mail.txt
            with open(mail_file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # Lọc các dòng không phải comment và không trống
            valid_lines = [line.strip() for line in lines if line.strip() and not line.strip().startswith("#")]

            # Nếu không có dòng hợp lệ, trả về None
            if not valid_lines:
                print("❌ Không có dòng hợp lệ trong file mail.txt")
                return None

            # Tính toán chỉ số dòng dựa trên chỉ số thiết bị và tổng số thiết bị
            line_index = self.device_index % len(valid_lines)
            selected_line = valid_lines[line_index]

            # Phân tích dòng để lấy mã xác thực (định dạng: email|password|authentication)
            parts = selected_line.split("|")
            if len(parts) >= 3:
                auth_secret = parts[2].strip()
                print(f"✅ Đã tải mã xác thực từ file mail.txt (dòng {line_index + 1}): {auth_secret[:4]}{'*' * (len(auth_secret) - 4) if len(auth_secret) > 4 else '*' * len(auth_secret)}")
                return auth_secret
            else:
                print(f"❌ Không có mã xác thực trong file mail.txt (dòng {line_index + 1}): {selected_line}")
                return None

        except Exception as e:
            print(f"❌ Lỗi khi tải mã xác thực từ file mail.txt: {str(e)}")
            return None

    def generate_otp_from_secret(self, secret):
        """
        Tạo mã OTP từ secret key.
        :param secret: Secret key để tạo OTP
        :return: Mã OTP 6 chữ số nếu thành công, None nếu thất bại
        """
        try:
            import pyotp

            # Loại bỏ khoảng trắng và chuyển thành chữ hoa
            clean_secret = secret.replace(" ", "").upper()

            # Tạo đối tượng TOTP
            totp = pyotp.TOTP(clean_secret)

            # Tạo mã OTP
            otp_code = totp.now()

            print(f"✅ Đã tạo mã OTP: {otp_code}")
            return otp_code

        except ImportError:
            print("❌ Không thể import thư viện pyotp. Vui lòng cài đặt: pip install pyotp")
            return None
        except Exception as e:
            print(f"❌ Lỗi khi tạo mã OTP từ secret: {str(e)}")
            return None

    def load_email_templates(self):
        """
        Đọc các mẫu kháng email từ file khangemail.txt
        :return: List các mẫu email
        """
        try:
            # Lấy đường dẫn đến file khangemail.txt trong thư mục data
            import path_manager
            template_file_path = path_manager.get_data_path("khangemail.txt")

            if not os.path.exists(template_file_path):
                print(f"❌ Không tìm thấy file khangemail.txt tại {template_file_path}")
                # Tạo file mẫu
                try:
                    with open(template_file_path, "w", encoding="utf-8") as f:
                        f.write("# Mẫu kháng email cho Google Play\n")
                        f.write("# Sử dụng {EMAIL} để thay thế email tự động\n")
                        f.write("# Mỗi mẫu cách nhau bởi dòng trống\n\n")
                        f.write("Kính gửi Google Play Support,\n\n")
                        f.write("Tôi là chủ sở hữu tài khoản {EMAIL} và tôi muốn khiếu nại về việc tài khoản bị khóa.\n")
                        f.write("Tôi cam kết sẽ tuân thủ các chính sách của Google Play.\n\n")
                        f.write("Trân trọng,\n")
                        f.write("Chủ tài khoản {EMAIL}\n\n")
                        f.write("---\n\n")
                        f.write("Dear Google Play Team,\n\n")
                        f.write("I am the owner of account {EMAIL} and I would like to appeal the account suspension.\n")
                        f.write("I promise to follow all Google Play policies going forward.\n\n")
                        f.write("Best regards,\n")
                        f.write("{EMAIL} Account Owner\n")
                    print(f"✅ Đã tạo file mẫu khangemail.txt tại: {template_file_path}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file mẫu khangemail.txt: {str(e)}")
                return []

            # Đọc file khangemail.txt
            with open(template_file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Tách các mẫu bằng dấu phân cách "---"
            templates = []
            parts = content.split("---")

            for part in parts:
                # Loại bỏ comment và dòng trống
                lines = []
                for line in part.split("\n"):
                    line = line.strip()
                    if line and not line.startswith("#"):
                        lines.append(line)

                if lines:
                    template = "\n".join(lines)
                    templates.append(template)

            print(f"✅ Đã đọc {len(templates)} mẫu kháng email từ file khangemail.txt")
            return templates

        except Exception as e:
            print(f"❌ Lỗi khi đọc file khangemail.txt: {str(e)}")
            return []

    def get_current_email(self):
        """
        Lấy email hiện tại ưu tiên từ cột data trên giao diện, fallback từ file mail.txt
        :return: Email string hoặc None
        """
        try:
            # Ưu tiên 1: Lấy email từ cột data trên giao diện
            if hasattr(self, 'data') and self.data:
                email_from_ui = self.data.strip()
                if email_from_ui and '@' in email_from_ui:
                    print(f"✅ Đã lấy email từ cột data trên giao diện: {email_from_ui}")
                    return email_from_ui
                else:
                    print(f"⚠️ Cột data trên giao diện không hợp lệ: '{email_from_ui}', fallback sang file mail.txt")
            else:
                print("⚠️ Không có data từ giao diện, fallback sang file mail.txt")

            # Ưu tiên 2: Lấy email từ file mail.txt
            import path_manager
            mail_file_path = path_manager.get_txt_path("mail.txt")

            if not os.path.exists(mail_file_path):
                print(f"❌ Không tìm thấy file mail.txt tại {mail_file_path}")
                return None

            # Đọc file mail.txt
            with open(mail_file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # Lọc các dòng hợp lệ
            valid_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith("#"):
                    valid_lines.append(line)

            if not valid_lines:
                print("❌ Không có dòng hợp lệ nào trong file mail.txt")
                return None

            # Tính toán chỉ số dòng dựa trên chỉ số thiết bị
            line_index = self.device_index % len(valid_lines)
            selected_line = valid_lines[line_index]

            # Phân tích dòng để lấy email (định dạng: email|password|authentication)
            parts = selected_line.split("|")
            if len(parts) >= 1:
                email = parts[0].strip()
                print(f"✅ Đã lấy email từ file mail.txt: {email}")
                return email
            else:
                print(f"❌ Định dạng dòng không hợp lệ trong file mail.txt: {selected_line}")
                return None

        except Exception as e:
            print(f"❌ Lỗi khi lấy email hiện tại: {str(e)}")
            return None

    def mark_email_as_verification_required(self):
        """Lưu email hiện tại vào file email_ver.txt khi phát hiện captcha"""
        try:
            current_email = self.get_current_email()
            if not current_email:
                print("⚠️ Không thể lấy email hiện tại để lưu vào email_ver.txt")
                return

            # Lấy đường dẫn file email_ver.txt (file riêng cho email bị verification)
            import path_manager
            email_ver_file_path = path_manager.get_data_path("email_ver.txt")

            # Tạo thư mục data nếu chưa tồn tại
            data_dir = os.path.dirname(email_ver_file_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # Đọc file hiện tại (nếu có)
            existing_emails = []
            if os.path.exists(email_ver_file_path):
                with open(email_ver_file_path, "r", encoding="utf-8") as f:
                    existing_emails = [line.strip() for line in f.readlines() if line.strip()]

            # Kiểm tra xem email đã có trong file chưa
            if current_email not in existing_emails:
                # Thêm email vào file email_ver.txt
                with open(email_ver_file_path, "a", encoding="utf-8") as f:
                    f.write(f"{current_email}\n")
                print(f"✅ Đã lưu email {current_email} vào file email_ver.txt (phát hiện captcha)")
            else:
                print(f"ℹ️ Email {current_email} đã có trong file email_ver.txt")

        except Exception as e:
            print(f"❌ Lỗi khi lưu email vào email_ver.txt: {str(e)}")

    def perform_email_appeal(self, check_stop_flag=None):
        """
        Thực hiện kháng email tự động
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Cập nhật status: Bắt đầu kháng email
            self.current_step = "📧 Bắt đầu kháng email"
            self.current_progress = "Đang chuẩn bị..."
            print(" Bắt đầu thực hiện kháng email tự động...")

            # Cập nhật trạng thái trong UI
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    return check_stop_flag()
                return False

            # Lấy email hiện tại
            current_email = self.get_current_email()
            if not current_email:
                print("❌ Không thể lấy email hiện tại để kháng")
                return False

            # Đọc mẫu kháng email
            templates = self.load_email_templates()
            if not templates:
                print("❌ Không có mẫu kháng email nào để sử dụng")
                return False

            # Random chọn một mẫu từ danh sách
            import random
            selected_template = random.choice(templates)
            template_index = templates.index(selected_template) + 1
            print(f"📝 Đã random chọn mẫu kháng email {template_index}/{len(templates)} (độ dài: {len(selected_template)} ký tự)")

            # Thay thế {EMAIL} bằng email thực
            appeal_content = selected_template.replace("{EMAIL}", current_email)
            print(f"✅ Đã tạo nội dung kháng email cho {current_email}")

            # Kiểm tra cờ dừng trước khi nhập nội dung
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhập nội dung kháng email")
                return False

            # Nhập nội dung kháng email
            print("⌨️ Đang nhập nội dung kháng email...")
            if self.input_text(appeal_content):
                print("✅ Đã nhập nội dung kháng email thành công")

                # Đợi một chút sau khi nhập
                if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập nội dung kháng email", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập nội dung")
                    return False

                # Tìm và tap nút gửi (có thể là submit.png, send.png, etc.)
                import path_manager
                submit_templates = ["submit.png", "send.png", "gui.png", "submit_appeal.png"]
                submit_found = False

                for submit_template in submit_templates:
                    submit_template_path = os.path.join(path_manager.get_template_path(), submit_template)
                    if os.path.exists(submit_template_path):
                        print(f"🔍 Tìm nút gửi: {submit_template}")

                        # Thử tìm nút gửi tối đa 3 lần
                        for attempt in range(3):
                            if should_stop():
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/3 tìm {submit_template}")
                                return False

                            print(f"Lần thử {attempt + 1}/3 tìm {submit_template}")
                            if self.find_image_and_tap(submit_template_path, check_stop_flag=check_stop_flag):
                                submit_found = True
                                print(f"✅ Đã tìm thấy và tap vào {submit_template}")
                                break
                            else:
                                if attempt < 2:
                                    if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại {submit_template}", check_stop_flag):
                                        return False

                        if submit_found:
                            break

                if submit_found:
                    print("✅ Đã gửi kháng email thành công")

                    # Đợi một chút sau khi gửi
                    if self._random_sleep(3.0, 5.0, "Chờ sau khi gửi kháng email", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi gửi")
                        return False

                    return True
                else:
                    print("⚠️ Không tìm thấy nút gửi - Kháng email có thể cần xử lý thủ công")
                    return False
            else:
                print("❌ Không thể nhập nội dung kháng email")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện kháng email: {str(e)}")
            return False

    def perform_email_appeal_with_steps(self, check_stop_flag=None):
        """
        Thực hiện kháng email tự động với các bước trung gian
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Cập nhật status: Bắt đầu kháng email
            self.current_step = "📧 Bắt đầu kháng email"
            self.current_progress = "Đang chuẩn bị..."
            print("📧 Bắt đầu thực hiện kháng email với các bước trung gian...")

            # Cập nhật trạng thái trong UI
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    return check_stop_flag()
                return False

            import path_manager

            # Bước 1: Tìm và tap vào next_khang.png
            self.current_step = "📧 Kháng email"
            self.current_progress = "Thực hiện kháng email..."
            print("🔍 Đang thực hiện kháng email tự động...")

            # Cập nhật trạng thái trong UI
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass
            next_khang_template_path = os.path.join(path_manager.get_template_path(), "next_khang.png")

            if os.path.exists(next_khang_template_path):
                next_khang_found = False
                # Thử tìm next_khang.png tối đa 5 lần
                for attempt in range(5):
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/5 tìm next_khang.png")
                        return False

                    print(f"Lần thử {attempt + 1}/5 tìm next_khang.png")
                    if self.find_image_and_tap(next_khang_template_path, check_stop_flag=check_stop_flag):
                        next_khang_found = True
                        print("✅ Đã tìm thấy và tap vào next_khang.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy next_khang.png trong lần thử {attempt + 1}")
                        if attempt < 4:
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại next_khang.png lần {attempt + 2}", check_stop_flag):
                                return False

                if not next_khang_found:
                    print("❌ Không thể tìm thấy next_khang.png sau 5 lần thử")
                    return False

                # Đợi một chút sau khi tap next_khang.png
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào next_khang.png", check_stop_flag):
                    return False
            else:
                print(f"❌ Không tìm thấy file template next_khang.png tại {next_khang_template_path}")
                return False

            # Bước 2: Tìm và tap vào enter_appeal.png
            self.current_step = "📧 Kháng email"
            self.current_progress = "Nhap noi dung..."
            print("🔍 Nhập nội dung khang email...")

            # Cập nhật trạng thái trong UI
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass
            enter_appeal_template_path = os.path.join(path_manager.get_template_path(), "enter_appeal.png")

            if os.path.exists(enter_appeal_template_path):
                enter_appeal_found = False
                # Thử tìm enter_appeal.png tối đa 5 lần
                for attempt in range(5):
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/5 tìm enter_appeal.png")
                        return False

                    print(f"Lần thử {attempt + 1}/5 tìm enter_appeal.png")
                    if self.find_image_and_tap(enter_appeal_template_path, check_stop_flag=check_stop_flag):
                        enter_appeal_found = True
                        print("✅ Đã tìm thấy và tap vào enter_appeal.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy enter_appeal.png trong lần thử {attempt + 1}")
                        if attempt < 4:
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại enter_appeal.png lần {attempt + 2}", check_stop_flag):
                                return False

                if not enter_appeal_found:
                    print("❌ Không thể tìm thấy enter_appeal.png sau 5 lần thử")
                    return False

                # Đợi một chút sau khi tap enter_appeal.png
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào enter_appeal.png", check_stop_flag):
                    return False
            else:
                print(f"❌ Không tìm thấy file template enter_appeal.png tại {enter_appeal_template_path}")
                return False

            # Bước 3: Nhập nội dung kháng email
            self.current_step = "📧 Kháng email"
            self.current_progress = " Nhập nội dung..."
            print("📝 Đang nhập nội dung kháng email")

            # Cập nhật trạng thái trong UI
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass

            # Lấy email hiện tại
            current_email = self.get_current_email()
            if not current_email:
                print("❌ Không thể lấy email hiện tại để kháng")
                return False

            # Đọc mẫu kháng email
            templates = self.load_email_templates()
            if not templates:
                print("❌ Không có mẫu kháng email nào để sử dụng")
                return False

            # Random chọn một mẫu từ danh sách
            import random
            selected_template = random.choice(templates)
            template_index = templates.index(selected_template) + 1
            print(f"📝 Đã random chọn mẫu kháng email {template_index}/{len(templates)} (độ dài: {len(selected_template)} ký tự)")

            # Thay thế {EMAIL} bằng email thực
            appeal_content = selected_template.replace("{EMAIL}", current_email)
            print(f"✅ Đã tạo nội dung kháng email cho {current_email}")

            # Kiểm tra cờ dừng trước khi nhập nội dung
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhập nội dung kháng email")
                return False

            # Nhập nội dung kháng email với next_khang
            self.current_step = "📧 Kháng email"
            self.current_progress = "Đang nhập nội dung..."
            print("⌨️ Đang nhập nội dung kháng email...")

            # Cập nhật trạng thái trong UI
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass

            if self.input_text_with_next_khang(appeal_content):
                # Cập nhật status: Hoàn thành kháng email
                self.current_step = "✅ Hoàn thành kháng email"
                self.current_progress = "Thành công!"
                print("✅ Đã hoàn thành nhập nội dung kháng email và các bước cuối")

                # Cập nhật trạng thái trong UI
                try:
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        self.update_status_func()
                except:
                    pass

                return True
            else:
                print("❌ Không thể nhập nội dung kháng email hoặc thực hiện các bước cuối")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện kháng email với các bước trung gian: {str(e)}")
            return False

    def _handle_step_5_5_and_beyond(self, check_stop_flag, should_stop):
        """Xử lý từ bước 5.5 trở đi (khi bỏ qua nhập password)"""
        try:
            # Đặt captcha_completed = False vì chúng ta bỏ qua captcha
            captcha_completed = False

            # Bước 5.5: Tìm và tap vào get_start.png (chỉ khi không vượt captcha)
            if not captcha_completed:
                print("Bước 5.5: Tìm và tap vào get_start.png")

                # Kiểm tra xem file template get_start.png có tồn tại không
                if not os.path.exists(self.get_start_template_path):
                    print(f"⚠️ Không tìm thấy file template get_start.png tại {self.get_start_template_path}")
                    template_dir = path_manager.get_template_path()
                    print(f"⚠️ Vui lòng đặt file get_start.png vào thư mục {template_dir}")
                    print("⚠️ Bỏ qua bước get_start và tiếp tục")
                else:
                    # Thử tìm get_start.png tối đa 2 lần
                    get_start_found = False
                    for attempt in range(2):
                        # Kiểm tra cờ dừng trước mỗi lần thử
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm get_start.png")
                            return False

                        print(f"Lần thử {attempt + 1}/2 tìm get_start.png")
                        if self.find_image_and_tap(self.get_start_template_path, check_stop_flag=check_stop_flag):
                            get_start_found = True
                            print("✅ Đã tìm thấy và tap vào get_start.png")
                            break
                        else:
                            print(f"❌ Không tìm thấy get_start.png trong lần thử {attempt + 1}")

                            # Đợi một chút trước khi thử lại
                            if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                                if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                    return False

                    # Nếu tìm thấy get_start.png, đợi một chút
                    if get_start_found:
                        # Đợi một chút sau khi tap vào get_start
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào get_start.png", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào get_start.png")
                            return False
                    else:
                        print("⚠️ Không tìm thấy get_start.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Bước 6: Tìm và tap vào use_pass.png (chỉ khi không vượt captcha)
            if not captcha_completed:
                print("Bước 6: Tìm và tap vào use_pass.png")

                # Kiểm tra xem file template use_pass.png có tồn tại không
                if not os.path.exists(self.use_pass_template_path):
                    print(f"⚠️ Không tìm thấy file template use_pass.png tại {self.use_pass_template_path}")
                    template_dir = path_manager.get_template_path()
                    print(f"⚠️ Vui lòng đặt file use_pass.png vào thư mục {template_dir}")
                    print("⚠️ Bỏ qua bước nhập mật khẩu lần 2 và tiếp tục")
                else:
                    # Thử tìm use_pass.png tối đa 2 lần
                    use_pass_found = False
                    for attempt in range(2):
                        # Kiểm tra cờ dừng trước mỗi lần thử
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm use_pass.png")
                            return False

                        print(f"Lần thử {attempt + 1}/2 tìm use_pass.png")
                        # Tăng threshold để chính xác hơn và thêm debug
                        found, location = self.find_image_on_screen(self.use_pass_template_path, threshold=0.9, check_stop_flag=check_stop_flag)
                        if found:
                            print(f"🎯 Tìm thấy use_pass.png tại tọa độ: {location}")
                            if self.tap_at_position(location[0], location[1], "use_pass.png"):
                                use_pass_found = True
                                print("✅ Đã tìm thấy và tap vào use_pass.png")
                                break
                            else:
                                print("❌ Không thể tap vào use_pass.png")
                        else:
                            print(f"❌ Không tìm thấy use_pass.png với threshold=0.9 trong lần thử {attempt + 1}")

                            # Đợi một chút trước khi thử lại
                            if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                                if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                    return False

                    # Nếu tìm thấy use_pass.png, nhập mật khẩu lần 2
                    if use_pass_found:
                        # Đợi một chút sau khi tap vào use_pass
                        if self._random_sleep(1.0, 2.0, "Chờ sau khi tap vào use_pass.png", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào use_pass.png")
                            return False

                        # Lấy mật khẩu từ thuộc tính password hoặc từ file mail.txt
                        password = self.load_password_from_mail_file()

                        if password:
                            print(f"🔑 Đã lấy mật khẩu để nhập: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")

                            # Nhập mật khẩu ngay lập tức sau khi tap use_pass
                            print("🔤 Đang nhập mật khẩu ngay sau khi tap use_pass...")
                            if self.input_text(password):
                                print("✅ Đã nhập mật khẩu thành công sau khi tap use_pass")

                                # Đợi một chút sau khi nhập mật khẩu
                                if self._random_sleep(1.0, 2.0, "Chờ sau khi nhập mật khẩu", check_stop_flag):
                                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập mật khẩu")
                                    return False

                                # Nhấn phím Enter sau khi nhập mật khẩu
                                print("⌨️ Đang nhấn phím Enter sau khi nhập mật khẩu...")
                                if self.press_key(66):  # KEYCODE_ENTER = 66
                                    print("✅ Đã nhấn phím Enter thành công")
                                else:
                                    print("❌ Không thể nhấn phím Enter")
                            else:
                                print("❌ Không thể nhập mật khẩu sau khi tap use_pass")
                        else:
                            print("⚠️ Không có mật khẩu để nhập")
                    else:
                        print("⚠️ Không tìm thấy use_pass.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Tiếp tục với các bước 7, 8, 9
            return self._handle_steps_7_8_9(check_stop_flag, should_stop)

        except Exception as e:
            print(f"❌ Lỗi trong _handle_step_5_5_and_beyond: {str(e)}")
            return False

    def _handle_steps_7_8_9(self, check_stop_flag, should_stop):
        """Xử lý các bước 7, 8, 9"""
        try:
            # Bước 7: Tìm và tap vào try_again.png
            print("Bước 7: Tìm và tap vào try_again.png")

            # Kiểm tra xem file template try_again.png có tồn tại không
            try_again_template_path = os.path.join(path_manager.get_template_path(), "try_again.png")
            if not os.path.exists(try_again_template_path):
                print(f"⚠️ Không tìm thấy file template try_again.png tại {try_again_template_path}")
                print("⚠️ Bỏ qua bước try_again và tiếp tục")
            else:
                # Thử tìm try_again.png tối đa 2 lần
                try_again_found = False
                for attempt in range(2):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm try_again.png")
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm try_again.png")
                    if self.find_image_and_tap(try_again_template_path, check_stop_flag=check_stop_flag):
                        try_again_found = True
                        print("✅ Đã tìm thấy và tap vào try_again.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy try_again.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy try_again.png, đợi một chút
                if try_again_found:
                    # Đợi một chút sau khi tap vào try_again
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào try_again.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào try_again.png")
                        return False
                else:
                    print("⚠️ Không tìm thấy try_again.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Bước 8: Tìm và tap vào contiue_chplay.png
            print("Bước 8: Tìm và tap vào contiue_chplay.png")

            # Kiểm tra xem file template contiue_chplay.png có tồn tại không
            continue_template_path = os.path.join(path_manager.get_template_path(), "contiue_chplay.png")
            if not os.path.exists(continue_template_path):
                print(f"⚠️ Không tìm thấy file template contiue_chplay.png tại {continue_template_path}")
                print("⚠️ Bỏ qua bước contiue_chplay và tiếp tục")
            else:
                # Thử tìm contiue_chplay.png tối đa 2 lần
                continue_found = False
                for attempt in range(2):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm contiue_chplay.png")
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm contiue_chplay.png")
                    if self.find_image_and_tap(continue_template_path, check_stop_flag=check_stop_flag):
                        continue_found = True
                        print("✅ Đã tìm thấy và tap vào contiue_chplay.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy contiue_chplay.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy contiue_chplay.png, đợi một chút
                if continue_found:
                    # Đợi một chút sau khi tap vào contiue_chplay
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào contiue_chplay.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào contiue_chplay.png")
                        return False
                else:
                    print("⚠️ Không tìm thấy contiue_chplay.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Bước 9: Tìm và tap vào allow.png
            print("Bước 9: Tìm và tap vào allow.png")

            # Kiểm tra xem file template allow.png có tồn tại không
            allow_template_path = os.path.join(path_manager.get_template_path(), "allow.png")
            if not os.path.exists(allow_template_path):
                print(f"⚠️ Không tìm thấy file template allow.png tại {allow_template_path}")
                print("⚠️ Bỏ qua bước allow và tiếp tục")
            else:
                # Thử tìm allow.png tối đa 2 lần
                allow_found = False
                for attempt in range(2):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm allow.png")
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm allow.png")
                    if self.find_image_and_tap(allow_template_path, check_stop_flag=check_stop_flag):
                        allow_found = True
                        print("✅ Đã tìm thấy và tap vào allow.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy allow.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy allow.png, đợi một chút
                if allow_found:
                    # Đợi một chút sau khi tap vào allow
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào allow.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào allow.png")
                        return False
                else:
                    print("⚠️ Không tìm thấy allow.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Đợi một chút trước khi kết thúc
            if self._random_sleep(3.0, 5.0, "Chờ trước khi kết thúc đăng nhập", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi kết thúc")
                return False

            print("✅ Đã hoàn thành quy trình đăng nhập Google Play (bao gồm bước 7, 8, 9)")
            return True

        except Exception as e:
            print(f"❌ Lỗi trong _handle_steps_7_8_9: {str(e)}")
            return False

    def wait_for_captcha_completion(self, check_stop_flag=None):
        """
        Chờ người dùng hoàn thành captcha và nhấn next.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu người dùng đã hoàn thành captcha, False nếu bị dừng
        """
        try:
            # Chờ người dùng hoàn thành captcha bằng cách kiểm tra xem welcome.png có xuất hiện không
            max_wait_time = 300  # Chờ tối đa 5 phút
            check_interval = 3   # Kiểm tra mỗi 3 giây
            elapsed_time = 0

            # Hiển thị thông báo đầu tiên ngay lập tức
            wait_msg = f"🤖 Có captcha,(0s) chờ vượt captcha... "
            print(wait_msg)

            while elapsed_time < max_wait_time:
                # Kiểm tra cờ dừng
                if check_stop_flag and callable(check_stop_flag):
                    if check_stop_flag():
                        return False

                # Kiểm tra xem welcome.png có xuất hiện không
                found, _ = self.find_image_on_screen(self.welcome_template_path, threshold=0.8, check_stop_flag=check_stop_flag)

                if found:
                    # Nếu thấy welcome.png, người dùng đã vượt qua captcha
                    return True

                time.sleep(check_interval)
                elapsed_time += check_interval

                # Cập nhật countdown cho UI mỗi 3 giây
                remaining_time = max_wait_time - elapsed_time
                remaining_minutes = remaining_time // 60
                remaining_seconds = remaining_time % 60

                if remaining_minutes > 0:
                    countdown_str = f"{remaining_minutes}p{remaining_seconds}s"
                else:
                    countdown_str = f"{remaining_seconds}s"

                # Cập nhật current_progress để hiển thị trong UI
                self.current_progress = f"({countdown_str}) Chờ vượt captcha..."

                # Cập nhật trạng thái trong UI
                try:
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        self.update_status_func()
                except:
                    pass

                # Cập nhật thời gian chờ mỗi 10 giây cho console
                if elapsed_time % 10 == 0:
                    minutes = elapsed_time // 60
                    seconds = elapsed_time % 60
                    if minutes > 0:
                        time_str = f"{minutes}p{seconds}s"
                    else:
                        time_str = f"{seconds}s"

                    wait_msg = f"🤖 Phát hiện captcha, chờ vượt captcha... ({time_str})"
                    print(wait_msg)

            # Nếu hết thời gian chờ
            timeout_msg = "⚠️ Đã hết thời gian chờ captcha (5 phút). Tiếp tục với quy trình..."
            print(timeout_msg)
            return True

        except Exception as e:
            print(f"❌ Lỗi khi chờ captcha: {str(e)}")
            return True  # Tiếp tục dù có lỗi

    def find_image_on_screen(self, template_path, threshold=0.8, roi=None, check_stop_flag=None):
        """
        Tìm hình ảnh trên màn hình nhưng không tap vào.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param roi: Region of Interest - Vùng quan tâm [x, y, width, height]. Nếu None, tìm trên toàn bộ màn hình.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: (True, location) nếu tìm thấy, (False, None) nếu không.
        """
        try:
            # Kiểm tra cờ dừng nếu có
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm {os.path.basename(template_path)}")
                    return False, None

            # Bắt đầu đo thời gian
            start_time = time.time()

            # Chụp ảnh màn hình
            screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")

            # Sử dụng exec-out để tăng tốc độ chụp ảnh
            with open(screenshot_path, "wb") as f:
                subprocess.run([ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                                stdout=f, check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Kiểm tra cờ dừng sau khi chụp ảnh
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi chụp ảnh màn hình")
                    return False, None

            # Đợi một chút sau khi chụp ảnh để đảm bảo file đã được lưu hoàn toàn
            time.sleep(0.5)

            # Đọc ảnh template và ảnh màn hình
            template = cv2.imread(template_path)
            screenshot = cv2.imread(screenshot_path)

            if template is None:
                print(f"❌ Không thể đọc ảnh template: {template_path}")
                return False, None

            if screenshot is None:
                print(f"❌ Không thể đọc ảnh màn hình: {screenshot_path}")
                # Thử đợi thêm một chút và đọc lại ảnh màn hình
                time.sleep(1.0)
                screenshot = cv2.imread(screenshot_path)
                if screenshot is None:
                    print(f"❌ Vẫn không thể đọc ảnh màn hình sau khi đợi thêm")
                    return False, None

            # Bỏ debug kích thước ảnh để tăng tốc

            # Chuyển đổi ảnh sang thang độ xám
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # Lấy kích thước template
            template_h, template_w = template_gray.shape

            # Nếu có ROI, cắt ảnh màn hình theo ROI
            if roi:
                x, y, w, h = roi
                screenshot_gray = screenshot_gray[y:y+h, x:x+w]
                roi_offset_x, roi_offset_y = x, y
            else:
                roi_offset_x, roi_offset_y = 0, 0

            # Tìm kiếm template trong ảnh màn hình
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Tính thời gian tổng cộng
            total_time = time.time() - start_time

            # Nếu độ khớp lớn hơn ngưỡng, trả về True và vị trí
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên màn hình
                center_x = roi_offset_x + max_loc[0] + template_w // 2
                center_y = roi_offset_y + max_loc[1] + template_h // 2

                return True, (center_x, center_y)
            else:
                return False, None

        except Exception as e:
            return False, None

    def find_image_and_tap(self, template_path, threshold=0.8, check_stop_flag=None, scale_factor=0.5):
        """
        Tìm vị trí ảnh trên màn hình và tap vào đó với xử lý ảnh nhẹ hơn.
        :param template_path: Đường dẫn đến file ảnh template
        :param threshold: Ngưỡng độ khớp (0.0-1.0)
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :param scale_factor: Hệ số thu nhỏ ảnh để tăng tốc xử lý (0.0-1.0)
        :return: True nếu tìm thấy và tap thành công, False nếu không tìm thấy
        """
        # Hàm kiểm tra cờ dừng
        def should_stop():
            if check_stop_flag and callable(check_stop_flag):
                stop_flag = check_stop_flag()
                if stop_flag:
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi tìm {os.path.basename(template_path)}")
                return stop_flag
            return False

        try:
            # Kiểm tra cờ dừng và file template một lần duy nhất trước khi bắt đầu
            if should_stop() or not os.path.exists(template_path):
                if not os.path.exists(template_path):
                    print(f"❌ Không tìm thấy file template: {template_path}")
                return False

            # Lấy đường dẫn đến thư mục screen
            screen_dir = path_manager.get_screen_path()

            # Chụp màn hình thiết bị trực tiếp vào bộ nhớ
            screenshot_path = os.path.join(screen_dir, f"screen_{self.serial}.png")
            try:
                # Sử dụng pipe để tránh ghi file tạm
                process = subprocess.Popen(
                    [ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                screenshot_data, _ = process.communicate()

                # Chuyển đổi dữ liệu ảnh thành mảng numpy
                screenshot_array = np.frombuffer(screenshot_data, dtype=np.uint8)
                screenshot = cv2.imdecode(screenshot_array, cv2.IMREAD_COLOR)

                # Lưu ảnh để debug nếu cần
                cv2.imwrite(screenshot_path, screenshot)
            except Exception as e:
                print(f"❌ Lỗi khi chụp màn hình: {str(e)}")
                return False

            if should_stop():
                return False

            # Đọc ảnh template
            template = cv2.imread(template_path)

            if template is None or screenshot is None:
                print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình")
                return False

            # Thu nhỏ ảnh để tăng tốc xử lý
            if scale_factor < 1.0:
                new_width = int(screenshot.shape[1] * scale_factor)
                new_height = int(screenshot.shape[0] * scale_factor)
                screenshot_resized = cv2.resize(screenshot, (new_width, new_height), interpolation=cv2.INTER_AREA)

                new_width = int(template.shape[1] * scale_factor)
                new_height = int(template.shape[0] * scale_factor)
                template_resized = cv2.resize(template, (new_width, new_height), interpolation=cv2.INTER_AREA)
            else:
                screenshot_resized = screenshot
                template_resized = template

            # Chuyển đổi ảnh sang thang độ xám để giảm kích thước dữ liệu
            template_gray = cv2.cvtColor(template_resized, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot_resized, cv2.COLOR_BGR2GRAY)

            # Tìm kiếm template trong ảnh màn hình với phương pháp hiệu quả
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên ảnh gốc
                h, w = template_gray.shape
                center_x = int(max_loc[0] / scale_factor + w / scale_factor / 2)
                center_y = int(max_loc[1] / scale_factor + h / scale_factor / 2)

                if should_stop():
                    return False

                # Tap vào vị trí tìm thấy
                subprocess.run(
                    [ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(center_x), str(center_y)],
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )

                # Đợi một chút sau khi tap
                if self._random_sleep(1.0, 2.0, "Chờ sau khi tap", check_stop_flag):
                    return False

                return True
            else:
                return False

        except Exception as e:
            return False

    def execute_actions(self, check_stop_flag=None):
        """
        Thực hiện chuỗi hành động: về màn hình chính, tìm và tap vào chplay.png
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Thông báo 1: Bắt đầu đăng nhập
            self.current_step = "Bắt đầu đăng nhập"
            self.current_progress = "Khởi tạo..."
            start_msg = f"🔄 Đang đăng nhập Google Play..."
            print(start_msg)

            # Cập nhật trạng thái trong AutoK.py nếu có
            try:
                # Gọi hàm cập nhật trạng thái nếu có
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                    self.update_status_func()
                else:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                    # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                    QApplication.processEvents()
            except:
                pass

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    # Kiểm tra cờ dừng
                    stop_flag = check_stop_flag()
                    return stop_flag
                return False

            # Kiểm tra cờ dừng trước khi bắt đầu
            if should_stop():
                return False

            # Bước 1: Về màn hình chính bằng cách nhấn nút Home
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "3"],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_HOME = 3

            # Đợi một chút sau khi về màn hình chính
            if self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi về màn hình chính")
                return False

            # Bước 2: Tìm và tap vào chplay.png
            self.current_step = "Đang đăng nhập Google Play"
            self.current_progress = "Đang tìm icon..."

            # Cập nhật trạng thái
            try:
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    self.update_status_func()
            except:
                pass

            # Kiểm tra xem file template chplay.png có tồn tại không
            if not os.path.exists(self.chplay_template_path):
                print(f"⚠️ Không tìm thấy file template chplay.png tại {self.chplay_template_path}")
                return False

            # Thử tìm chplay.png tối đa 5 lần
            chplay_found = False
            for attempt in range(5):
                # Kiểm tra cờ dừng trước mỗi lần thử
                if should_stop():
                    return False

                self.current_progress = f"Đang tìm... ({attempt + 1}/5)"
                # Cập nhật trạng thái
                try:
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        self.update_status_func()
                except:
                    pass

                if self.find_image_and_tap(self.chplay_template_path, check_stop_flag=check_stop_flag):
                    chplay_found = True
                    break
                else:
                    # Đợi một chút trước khi thử lại
                    if attempt < 4:
                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                            return False

            # Nếu không tìm thấy chplay.png sau tất cả các lần thử, thông báo lỗi và dừng
            if not chplay_found:
                print("❌ Không thể tìm thấy chplay.png sau 5 lần thử")
                return False

            # Đợi ứng dụng Google Play khởi động
            if self._random_sleep(15.0, 17.0, "Chờ ứng dụng Google Play khởi động", check_stop_flag):
                return False

            # Đợi thêm một chút để giao diện Google Play hiển thị hoàn toàn
            if self._random_sleep(2.0, 3.0, "Chờ giao diện Google Play hiển thị", check_stop_flag):
                return False

            # Bước 3: Tìm và tap vào next.png
            # Kiểm tra xem file template next.png có tồn tại không
            if not os.path.exists(self.next_template_path):
                print(f"⚠️ Không tìm thấy file template next.png tại {self.next_template_path}")
                return False

            # Thử tìm next.png tối đa 7 lần
            next_found = False
            for attempt in range(7):
                # Kiểm tra cờ dừng trước mỗi lần thử
                if should_stop():
                    return False

                if self.find_image_and_tap(self.next_template_path, check_stop_flag=check_stop_flag):
                    next_found = True
                    break
                else:
                    # Đợi một chút trước khi thử lại
                    if attempt < 4:
                        if self._random_sleep(3.0, 4.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                            return False

            # Nếu không tìm thấy next.png, bỏ qua các bước 3.5, 4, 5 và nhảy thẳng đến bước 5.5
            if not next_found:
                print("⚠️ Không tìm thấy next.png - Bỏ qua bước nhập password và nhảy thẳng đến bước 5.5")
                # Đợi một chút trước khi nhảy đến bước 5.5
                if self._random_sleep(2.0, 3.0, "Chờ trước khi nhảy đến bước 5.5", check_stop_flag):
                    return False
                # Nhảy thẳng đến bước 5.5 (tìm get_start.png)
                return self._handle_step_5_5_and_beyond(check_stop_flag, should_stop)

            # Đợi một chút sau khi tap vào next
            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap next.png", check_stop_flag):
                return False

            # Bước 3.5: Kiểm tra captcha (not_robot.png)

            # Biến để theo dõi xem có vượt captcha thành công không
            captcha_completed = False

            # Kiểm tra xem có captcha không
            if os.path.exists(self.not_robot_template_path):
                # Thử tìm not_robot.png để kiểm tra có captcha không
                captcha_found = False
                for attempt in range(3):  # Chỉ thử 3 lần để không mất quá nhiều thời gian
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        return False

                    found, _ = self.find_image_on_screen(self.not_robot_template_path, threshold=0.8, check_stop_flag=check_stop_flag)

                    if found:
                        captcha_found = True
                        # Đánh dấu email cần verification
                        self.mark_email_as_verification_required()

                        # Thông báo 2: Phát hiện captcha
                        self.current_step = "🤖 Phát hiện captcha"
                        self.current_progress = "Chờ vượt captcha..."
                        captcha_msg = "🤖 Phát hiện captcha, chờ vượt captcha..."
                        print(captcha_msg)

                        # Cập nhật trạng thái trong AutoK.py nếu có
                        try:
                            # Gọi hàm cập nhật trạng thái nếu có
                            if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                                self.update_status_func()
                                # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                                self.update_status_func()
                            else:
                                from PyQt5.QtWidgets import QApplication
                                QApplication.processEvents()
                                # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                                QApplication.processEvents()
                        except:
                            pass
                        break
                    else:
                        # Đợi một chút trước khi thử lại
                        if attempt < 2:
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi kiểm tra captcha lần {attempt + 2}", check_stop_flag):
                                return False

                # Nếu phát hiện captcha, chờ người dùng hoàn thành
                if captcha_found:
                    if not self.wait_for_captcha_completion(check_stop_flag):
                        stop_captcha_msg = "⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ captcha"
                        print(stop_captcha_msg)
                        return False
                    captcha_completed = True  # Đánh dấu đã vượt captcha thành công

                    # Thông báo 3: Đã vượt captcha thành công
                    self.current_step = " Vượt captcha thành công"
                    self.current_progress = "Tiếp tục đăng nhập..."
                    success_captcha_msg = "🔄 Vượt captcha thành công, tiếp tục đăng nhập"
                    print(success_captcha_msg)

                    # Cập nhật trạng thái trong AutoK.py nếu có
                    try:
                        # Gọi hàm cập nhật trạng thái nếu có
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass
                else:
                    pass  # Không phát hiện captcha, tiếp tục quy trình bình thường
            else:
                pass  # Bỏ qua bước kiểm tra captcha nếu không có file template

            # Bước 4: Nhập mật khẩu (tìm welcome.png nếu đã vượt captcha, hoặc tìm enter_your_pass.png nếu không có captcha)
            if captcha_completed:
                # Kiểm tra xem file template welcome.png có tồn tại không
                if not os.path.exists(self.welcome_template_path):
                    print(f"⚠️ Không tìm thấy file template welcom.png tại {self.welcome_template_path}")
                    return False

                # Thử tìm welcome.png tối đa 5 lần
                welcome_found = False
                for attempt in range(5):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        return False

                    found, _ = self.find_image_on_screen(self.welcome_template_path, threshold=0.8, check_stop_flag=check_stop_flag)

                    if found:
                        welcome_found = True
                        break
                    else:
                        # Đợi một chút trước khi thử lại
                        if attempt < 4:
                            if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                return False

                # Nếu tìm thấy welcome.png, nhập mật khẩu trực tiếp
                if welcome_found:
                    # Lấy mật khẩu từ thuộc tính password hoặc từ file mail.txt
                    password = self.load_password_from_mail_file()

                    if password:
                        # Nhập mật khẩu trực tiếp
                        if self.input_text(password):
                            print("✅ Đã nhập mật khẩu trực tiếp thành công")

                            # Đợi một chút sau khi nhập mật khẩu
                            if self._random_sleep(1.0, 2.0, "Chờ sau khi nhập mật khẩu trực tiếp", check_stop_flag):
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập mật khẩu trực tiếp")
                                return False

                            # Nhấn phím Enter
                            if self.press_key(66):  # KEYCODE_ENTER = 66
                                print("✅ Đã nhấn phím Enter sau khi nhập mật khẩu trực tiếp")
                            else:
                                print("❌ Không thể nhấn phím Enter sau khi nhập mật khẩu trực tiếp")
                        else:
                            print("❌ Không thể nhập mật khẩu trực tiếp")
                            return False
                    else:
                        no_password_msg = "❌ Không thể lấy mật khẩu từ giao diện hoặc file mail.txt"
                        print(no_password_msg)
                        return False
                else:
                    no_welcome_after_tries_msg = "⚠️ Không tìm thấy welcome.png sau 5 lần thử"
                    print(no_welcome_after_tries_msg)
                    return False
            else:
                print("Bước 4: Tìm và tap vào enter_your_pass.png")

                # Kiểm tra xem file template enter_your_pass.png có tồn tại không
                if not os.path.exists(self.enter_pass_template_path):
                    print(f"⚠️ Không tìm thấy file template enter_your_pass.png tại {self.enter_pass_template_path}")
                    template_dir = path_manager.get_template_path()
                    print(f"⚠️ Vui lòng đặt file enter_your_pass.png vào thư mục {template_dir}")
                    return False

                # Thử tìm enter_your_pass.png tối đa 3 lần
                enter_pass_found = False
                for attempt in range(3):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/5 tìm enter_your_pass.png")
                        return False

                    print(f"Lần thử {attempt + 1}/5 tìm enter_your_pass.png")
                    if self.find_image_and_tap(self.enter_pass_template_path, check_stop_flag=check_stop_flag):
                        enter_pass_found = True
                        print("✅ Đã tìm thấy và tap vào enter_your_pass.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy enter_your_pass.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 4:
                            if self._random_sleep(2.0, 4.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy enter_your_pass.png, nhập mật khẩu
                if enter_pass_found:
                    # Đợi một chút sau khi tap vào enter_your_pass
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào enter_your_pass.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào enter_your_pass.png")
                        return False

                    # Lấy mật khẩu từ thuộc tính password hoặc từ file mail.txt
                    password = self.load_password_from_mail_file()

                    if password:
                        # Nhập mật khẩu
                        if self.input_text(password):
                            print("✅ Đã nhập mật khẩu thành công")

                            # Đợi một chút sau khi nhập mật khẩu
                            if self._random_sleep(1.0, 2.0, "Chờ sau khi nhập mật khẩu", check_stop_flag):
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập mật khẩu")
                                return False

                            # Nhấn phím Enter
                            if self.press_key(66):  # KEYCODE_ENTER = 66
                                print("✅ Đã nhấn phím Enter sau khi nhập mật khẩu")
                            else:
                                print("❌ Không thể nhấn phím Enter sau khi nhập mật khẩu")
                        else:
                            print("❌ Không thể nhập mật khẩu")
                    else:
                        print("❌ Không thể lấy mật khẩu từ giao diện hoặc file mail.txt")
                else:
                    print("⚠️ Không tìm thấy enter_your_pass.png sau 5 lần thử, nhưng vẫn tiếp tục")

            # Đợi một chút sau khi xử lý enter_your_pass.png
            if self._random_sleep(3.0, 5.0, "Chờ sau khi xử lý enter_your_pass.png", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý enter_your_pass.png")
                return False

            # Bước 5: Tìm và tap vào authen.png
            print("Bước 5: Tìm và tap vào authen.png")

            # Kiểm tra xem file template authen.png có tồn tại không
            if not os.path.exists(self.authen_template_path):
                print(f"⚠️ Không tìm thấy file template authen.png tại {self.authen_template_path}")
                template_dir = path_manager.get_template_path()
                print(f"⚠️ Vui lòng đặt file authen.png vào thư mục {template_dir}")
                print("⚠️ Bỏ qua bước xác thực và tiếp tục")
            else:
                # Thử tìm authen.png tối đa 5 lần
                authen_found = False
                for attempt in range(5):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/5 tìm authen.png")
                        return False

                    print(f"Lần thử {attempt + 1}/5 tìm authen.png")
                    if self.find_image_and_tap(self.authen_template_path, check_stop_flag=check_stop_flag):
                        authen_found = True
                        print("✅ Đã tìm thấy và tap vào authen.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy authen.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 4:
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy authen.png, nhập mã OTP
                if authen_found:
                    # Đợi một chút sau khi tap vào authen
                    if self._random_sleep(3.0, 4.0, "Chờ sau khi tap vào authen.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào authen.png")
                        return False

                    # Lấy mã xác thực từ file mail.txt
                    auth_secret = self.load_auth_secret_from_mail_file()

                    if auth_secret:
                        # Tạo mã OTP từ secret
                        otp_code = self.generate_otp_from_secret(auth_secret)

                        if otp_code:
                            # Nhập mã OTP
                            if self.input_text(otp_code):
                                print("✅ Đã nhập mã OTP thành công")

                                # Đợi một chút sau khi nhập mã OTP
                                if self._random_sleep(3.0, 5.0, "Chờ sau khi nhập mã OTP", check_stop_flag):
                                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập mã OTP")
                                    return False

                                # Nhấn phím Enter
                                if self.press_key(66):  # KEYCODE_ENTER = 66
                                    print("✅ Đã nhấn phím Enter sau khi nhập mã OTP")
                                else:
                                    print("❌ Không thể nhấn phím Enter sau khi nhập mã OTP")
                            else:
                                print("❌ Không thể nhập mã OTP")
                        else:
                            print("❌ Không thể tạo mã OTP từ secret")
                    else:
                        print("❌ Không thể lấy mã xác thực từ file mail.txt")
                else:
                    print("⚠️ Không tìm thấy authen.png sau 5 lần thử, nhưng vẫn tiếp tục")

            # Đợi một chút sau khi xử lý authen.png
            if self._random_sleep(6.0, 7.0, "Chờ sau khi xử lý authen.png", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý authen.png")
                return False

            # Bước 5.5: Tìm và tap vào get_start.png (chỉ khi không vượt captcha)
            if not captcha_completed:
                print("Bước 5.5: Tìm và tap vào get_start.png")

                # Kiểm tra xem file template get_start.png có tồn tại không
                if not os.path.exists(self.get_start_template_path):
                    print(f"⚠️ Không tìm thấy file template get_start.png tại {self.get_start_template_path}")
                    template_dir = path_manager.get_template_path()
                    print(f"⚠️ Vui lòng đặt file get_start.png vào thư mục {template_dir}")
                    print("⚠️ Bỏ qua bước get_start và tiếp tục")
                else:
                    # Thử tìm get_start.png tối đa 2 lần
                    get_start_found = False
                    for attempt in range(2):
                        # Kiểm tra cờ dừng trước mỗi lần thử
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm get_start.png")
                            return False

                        print(f"Lần thử {attempt + 1}/2 tìm get_start.png")
                        if self.find_image_and_tap(self.get_start_template_path, check_stop_flag=check_stop_flag):
                            get_start_found = True
                            print("✅ Đã tìm thấy và tap vào get_start.png")
                            break
                        else:
                            print(f"❌ Không tìm thấy get_start.png trong lần thử {attempt + 1}")

                            # Đợi một chút trước khi thử lại
                            if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                                if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                    return False

                    # Nếu tìm thấy get_start.png, đợi một chút
                    if get_start_found:
                        # Đợi một chút sau khi tap vào get_start
                        if self._random_sleep(4.0, 5.0, "Chờ sau khi tap vào get_start.png", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào get_start.png")
                            return False
                    else:
                        print("⚠️ Không tìm thấy get_start.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Bước 6: Tìm và tap vào use_pass.png (chỉ khi không vượt captcha)
            if not captcha_completed:
                print("Bước 6: Tìm và tap vào use_pass.png")

                # Kiểm tra xem file template use_pass.png có tồn tại không
                if not os.path.exists(self.use_pass_template_path):
                    print(f"⚠️ Không tìm thấy file template use_pass.png tại {self.use_pass_template_path}")
                    template_dir = path_manager.get_template_path()
                    print(f"⚠️ Vui lòng đặt file use_pass.png vào thư mục {template_dir}")
                    print("⚠️ Bỏ qua bước nhập mật khẩu lần 2 và tiếp tục")
                else:
                    # Thử tìm use_pass.png tối đa 2 lần
                    use_pass_found = False
                    for attempt in range(2):
                        # Kiểm tra cờ dừng trước mỗi lần thử
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm use_pass.png")
                            return False

                        print(f"Lần thử {attempt + 1}/2 tìm use_pass.png")
                        if self.find_image_and_tap(self.use_pass_template_path, check_stop_flag=check_stop_flag):
                            use_pass_found = True
                            print("✅ Đã tìm thấy và tap vào use_pass.png")
                            break
                        else:
                            print(f"❌ Không tìm thấy use_pass.png trong lần thử {attempt + 1}")

                            # Đợi một chút trước khi thử lại
                            if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                                if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                    return False

                    # Nếu tìm thấy use_pass.png, nhập mật khẩu lần 2
                    if use_pass_found:
                        # Đợi một chút sau khi tap vào use_pass
                        if self._random_sleep(4.0, 5.0, "Chờ sau khi tap vào use_pass.png", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào use_pass.png")
                            return False

                        # Lấy mật khẩu từ thuộc tính password hoặc từ file mail.txt
                        password = self.load_password_from_mail_file()

                        if password:
                            print(f"🔑 Đã lấy mật khẩu để nhập: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")

                            # Nhập mật khẩu ngay lập tức sau khi tap use_pass
                            print("🔤 Đang nhập mật khẩu ngay sau khi tap use_pass...")
                            if self.input_text(password):
                                print("✅ Đã nhập mật khẩu thành công sau khi tap use_pass")

                                # Đợi một chút sau khi nhập mật khẩu
                                if self._random_sleep(1.0, 2.0, "Chờ sau khi nhập mật khẩu", check_stop_flag):
                                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập mật khẩu")
                                    return False

                                # Nhấn phím Enter sau khi nhập mật khẩu
                                print("⌨️ Đang nhấn phím Enter sau khi nhập mật khẩu...")
                                if self.press_key(66):  # KEYCODE_ENTER = 66
                                    print("✅ Đã nhấn phím Enter thành công")
                                else:
                                    print("❌ Không thể nhấn phím Enter")
                            else:
                                print("❌ Không thể nhập mật khẩu sau khi tap use_pass")
                        else:
                            print("❌ Không thể lấy mật khẩu từ giao diện hoặc file mail.txt")
                    else:
                        print("⚠️ Không tìm thấy use_pass.png sau 2 lần thử, nhưng vẫn tiếp tục")

                # Đợi một chút sau khi xử lý use_pass.png
                if self._random_sleep(3.0, 5.0, "Chờ sau khi xử lý use_pass.png", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý use_pass.png")
                    return False
            else:
                # Nếu đã vượt captcha, bỏ qua bước use_pass.png
                skip_msg = "✅ Đã vượt captcha thành công, bỏ qua bước nhập mật khẩu lần 2 (use_pass.png)"
                print(skip_msg)

            # Bước 7: Tìm và tap vào try_again.png
            print("Bước 7: Tìm và tap vào try_again.png")

            # Kiểm tra xem file template try_again.png có tồn tại không
            try_again_template_path = os.path.join(path_manager.get_template_path(), "try_again.png")
            if not os.path.exists(try_again_template_path):
                print(f"⚠️ Không tìm thấy file template try_again.png tại {try_again_template_path}")
                print("⚠️ Bỏ qua bước try_again và tiếp tục")
            else:
                # Thử tìm try_again.png tối đa 2 lần
                try_again_found = False
                for attempt in range(2):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm try_again.png")
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm try_again.png")
                    if self.find_image_and_tap(try_again_template_path, check_stop_flag=check_stop_flag):
                        try_again_found = True
                        print("✅ Đã tìm thấy và tap vào try_again.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy try_again.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy try_again.png, đợi một chút
                if try_again_found:
                    # Đợi một chút sau khi tap vào try_again
                    if self._random_sleep(4.0, 5.0, "Chờ sau khi tap vào try_again.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào try_again.png")
                        return False
                else:
                    print("⚠️ Không tìm thấy try_again.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Bước 8: Tìm và tap vào contiue_chplay.png
            print("Bước 8: Tìm và tap vào contiue_chplay.png")

            # Kiểm tra xem file template contiue_chplay.png có tồn tại không
            continue_template_path = os.path.join(path_manager.get_template_path(), "contiue_chplay.png")
            if not os.path.exists(continue_template_path):
                print(f"⚠️ Không tìm thấy file template contiue_chplay.png tại {continue_template_path}")
                print("⚠️ Bỏ qua bước contiue_chplay và tiếp tục")
            else:
                # Thử tìm contiue_chplay.png tối đa 3 lần
                continue_found = False
                for attempt in range(3):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm contiue_chplay.png")
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm contiue_chplay.png")
                    if self.find_image_and_tap(continue_template_path, check_stop_flag=check_stop_flag):
                        continue_found = True
                        print("✅ Đã tìm thấy và tap vào contiue_chplay.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy contiue_chplay.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy contiue_chplay.png, đợi một chút
                if continue_found:
                    # Đợi một chút sau khi tap vào contiue_chplay
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào contiue_chplay.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào contiue_chplay.png")
                        return False
                else:
                    print("⚠️ Không tìm thấy contiue_chplay.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Bước 9: Tìm và tap vào allow.png
            print("Bước 9: Tìm và tap vào allow.png")

            # Kiểm tra xem file template allow.png có tồn tại không
            allow_template_path = os.path.join(path_manager.get_template_path(), "allow.png")
            if not os.path.exists(allow_template_path):
                print(f"⚠️ Không tìm thấy file template allow.png tại {allow_template_path}")
                print("⚠️ Bỏ qua bước allow và tiếp tục")
            else:
                # Thử tìm allow.png tối đa 2 lần
                allow_found = False
                for attempt in range(2):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/2 tìm allow.png")
                        return False

                    print(f"Lần thử {attempt + 1}/2 tìm allow.png")
                    if self.find_image_and_tap(allow_template_path, check_stop_flag=check_stop_flag):
                        allow_found = True
                        print("✅ Đã tìm thấy và tap vào allow.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy allow.png trong lần thử {attempt + 1}")

                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng (attempt 0 < 1)
                            if self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                return False

                # Nếu tìm thấy allow.png, đợi một chút
                if allow_found:
                    # Đợi một chút sau khi tap vào allow
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào allow.png", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào allow.png")
                        return False
                else:
                    print("⚠️ Không tìm thấy allow.png sau 2 lần thử, nhưng vẫn tiếp tục")

            # Thông báo 4: Hoàn thành đăng nhập
            self.current_step = "✅ Hoàn thành đăng nhập"
            self.current_progress = "Thành công!"
            complete_msg = "✅ Hoàn thành đăng nhập Google Play"
            print(complete_msg)

            # Cập nhật trạng thái trong AutoK.py nếu có
            try:
                # Gọi hàm cập nhật trạng thái nếu có
                if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                    self.update_status_func()
                    # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                    self.update_status_func()
                else:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                    # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                    QApplication.processEvents()
            except:
                pass

            # Bước 7: Chỉ tìm start_appeal.png nếu đã phát hiện và xử lý xong captcha
            if captcha_completed:
                print("Bước 7: Đã vượt captcha thành công - Đợi load web và tìm start_appeal.png")

                # Đợi web load
                if self._random_sleep(5.0, 8.0, "Chờ web load sau khi vượt captcha", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ web load")
                    return False

                # Tìm start_appeal.png
                start_appeal_template_path = os.path.join(path_manager.get_template_path(), "start_appeal.png")

                if os.path.exists(start_appeal_template_path):
                    print("🔍 Tìm start_appeal.png để bắt đầu kháng email...")

                    # Thử tìm start_appeal.png tối đa 10 lần (chờ web load)
                    start_appeal_found = False
                    for attempt in range(10):
                        # Kiểm tra cờ dừng trước mỗi lần thử
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/10 tìm start_appeal.png")
                            return False

                        print(f"Lần thử {attempt + 1}/10 tìm start_appeal.png")
                        found, _ = self.find_image_on_screen(start_appeal_template_path, threshold=0.8, check_stop_flag=check_stop_flag)

                        if found:
                            start_appeal_found = True
                            print("✅ Đã tìm thấy start_appeal.png - Bắt đầu kháng email")
                            break
                        else:
                            print(f"❌ Không tìm thấy start_appeal.png trong lần thử {attempt + 1}")
                            # Đợi lâu hơn giữa các lần thử vì web cần thời gian load
                            if attempt < 9:
                                if self._random_sleep(3.0, 5.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại")
                                    return False

                    # Nếu tìm thấy start_appeal.png, thực hiện kháng email
                    if start_appeal_found:
                        # Tap vào start_appeal.png
                        if self.find_image_and_tap(start_appeal_template_path, check_stop_flag=check_stop_flag):
                            print("✅ Đã tap vào start_appeal.png")

                            # Đợi một chút sau khi tap
                            if self._random_sleep(3.0, 5.0, "Chờ sau khi tap vào start_appeal.png", check_stop_flag):
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào start_appeal.png")
                                return False

                            # Thực hiện kháng email với các bước trung gian
                            appeal_result = self.perform_email_appeal_with_steps(check_stop_flag)
                            if appeal_result:
                                print("✅ Đã hoàn thành kháng email thành công")
                            else:
                                print("⚠️ Kháng email không thành công hoặc bị dừng")
                        else:
                            print("❌ Không thể tap vào start_appeal.png")
                    else:
                        print("ℹ️ Không tìm thấy start_appeal.png sau 10 lần thử - Có thể không cần kháng email")
                else:
                    print(f"ℹ️ Không tìm thấy file template start_appeal.png - Bỏ qua bước kháng email")
            else:
                print("ℹ️ Không phát hiện captcha - Bỏ qua bước tìm start_appeal.png")

            print("✅ Đã hoàn thành việc đăng nhập Google Play")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False

class ChPlayThread(QThread):
    """
    Luồng để thực hiện chức năng đăng nhập bằng Google Play.
    """
    # Tín hiệu để thông báo tiến trình
    progress = pyqtSignal(str)
    # Tín hiệu để thông báo kết quả
    result = pyqtSignal(bool, str)
    # Tín hiệu để thông báo hoàn thành
    finished = pyqtSignal()

    def __init__(self, serial, device_index, total_devices, adb_path=None, password=None, auth_secret=None, data=None):
        """
        Khởi tạo luồng ChPlayThread.
        :param serial: Số serial của thiết bị
        :param device_index: Chỉ số thiết bị
        :param total_devices: Tổng số thiết bị
        :param adb_path: Đường dẫn đến ADB
        :param password: Mật khẩu (nếu có)
        :param auth_secret: Mã xác thực (nếu có)
        :param data: Data từ cột số 4 (nếu có)
        """
        super().__init__()
        self.serial = serial
        self.device_index = device_index
        self.total_devices = total_devices
        self.adb_path = adb_path
        self.password = password
        self.auth_secret = auth_secret
        self.data = data
        self.stop_flag = threading.Event()

    def run(self):
        """
        Thực hiện chức năng đăng nhập bằng Google Play.
        """
        try:
            # Thông báo bắt đầu
            start_msg = f"🔄 Bắt đầu đăng nhập Google Play trên thiết bị {self.serial}"
            self.progress.emit(start_msg)

            # Tạo đối tượng ChPlayLogin
            chplay_instance = ChPlayLogin(self.serial, 1, self.device_index, self.total_devices, self.adb_path, self.password, self.auth_secret, self.data)

            # Expose chplay_instance để AutoK.py có thể truy cập
            self.chplay_instance = chplay_instance

            # Set update_status_func như mua.py và addads.py - CHỈ CẬP NHẬT BIẾN, KHÔNG CẬP NHẬT UI
            def update_status():
                # Chỉ force UI update như mua.py, không trực tiếp cập nhật UI từ worker thread
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                    QApplication.processEvents()
                except Exception as e:
                    pass

            # Gán hàm update_status vào chplay_instance
            chplay_instance.update_status_func = update_status

            # Tạo hàm kiểm tra cờ dừng
            def check_stop_flag():
                return self.stop_flag.is_set()

            # Thực hiện chuỗi hành động - chỉ dùng cách của mua.py
            result = chplay_instance.execute_actions(check_stop_flag)

            # Thông báo kết quả
            if result:
                result_msg = f"✅ Đã đăng nhập Google Play thành công trên thiết bị {self.serial}"
                self.progress.emit(result_msg)
                self.result.emit(True, self.serial)
            else:
                result_msg = f"❌ Đăng nhập Google Play thất bại trên thiết bị {self.serial}"
                self.progress.emit(result_msg)
                self.result.emit(False, self.serial)

        except Exception as e:
            # Xử lý lỗi
            error_msg = f"Lỗi khi đăng nhập Google Play: {str(e)}"
            self.progress.emit(f"❌ {error_msg} - thiết bị {self.serial}")
            self.result.emit(False, self.serial)

        finally:
            # Phát tín hiệu hoàn thành
            self.finished.emit()

    def stop(self):
        """
        Dừng luồng.
        """
        self.stop_flag.set()
        self.progress.emit(f"⚠️ Đã gửi tín hiệu dừng đến thiết bị {self.serial}")

class ChPlayMultiThread:
    """
    Lớp để thực hiện đăng nhập Google Play trên nhiều thiết bị cùng lúc.
    """
    def __init__(self, max_workers=5):
        """
        Khởi tạo ChPlayMultiThread.
        :param max_workers: Số lượng luồng tối đa chạy đồng thời
        """
        self.max_workers = max_workers
        self.threads = {}
        self.results = {}
        self.lock = threading.Lock()

    def start(self, devices, result_callback=None, finished_callback=None, adb_path=None):
        """
        Bắt đầu đăng nhập Google Play trên nhiều thiết bị.
        :param devices: Danh sách các thiết bị [(row, serial, password, auth_secret), ...]
        :param result_callback: Hàm callback để thông báo kết quả
        :param finished_callback: Hàm callback để thông báo hoàn thành
        :param adb_path: Đường dẫn đến ADB
        """
        # Khởi tạo kết quả
        self.results = {serial: None for _, serial, _, _, _ in devices}

        # Tạo và khởi động các luồng
        for row, serial, password, auth_secret, data in devices:
            # Tạo luồng mới - dùng cách của mua.py
            thread = ChPlayThread(serial, row, len(devices), adb_path, password, auth_secret, data)

            # Kết nối tín hiệu kết quả
            if result_callback:
                from PyQt5.QtCore import Qt
                thread.result.connect(lambda success, dev_serial=serial: self._on_result(success, dev_serial, result_callback), Qt.QueuedConnection)

            # Kết nối tín hiệu hoàn thành
            from PyQt5.QtCore import Qt
            thread.finished.connect(lambda dev_serial=serial: self._on_thread_finished(dev_serial, finished_callback), Qt.QueuedConnection)

            # Lưu luồng
            self.threads[serial] = thread

            # Khởi động luồng
            thread.start()

            # Bỏ thông báo duplicate để tránh ghi đè thông báo captcha

    def _on_result(self, success, serial, callback):
        """
        Xử lý khi có kết quả từ một luồng.
        :param success: True nếu thành công, False nếu thất bại
        :param serial: Số serial của thiết bị
        :param callback: Hàm callback để thông báo kết quả
        """
        with self.lock:
            self.results[serial] = success

        # Gọi callback
        if callback:
            callback(success, serial)

    def _on_thread_finished(self, serial, callback):
        """
        Xử lý khi một luồng hoàn thành.
        :param serial: Số serial của thiết bị
        :param callback: Hàm callback để thông báo hoàn thành
        """
        # Xóa luồng
        with self.lock:
            if serial in self.threads:
                del self.threads[serial]

            # Kiểm tra xem tất cả các luồng đã hoàn thành chưa
            if not self.threads and callback:
                callback(self.results)

    def stop_all(self):
        """
        Dừng tất cả các luồng.
        :return: Số lượng luồng đã dừng
        """
        count = 0
        with self.lock:
            for serial, thread in list(self.threads.items()):
                thread.stop()
                count += 1
        return count

    def get_results(self):
        """
        Lấy kết quả của tất cả các luồng.
        :return: Dictionary {serial: result}
        """
        with self.lock:
            return self.results.copy()

    def is_running(self):
        """
        Kiểm tra xem có luồng nào đang chạy không.
        :return: True nếu có luồng đang chạy, False nếu không
        """
        with self.lock:
            return len(self.threads) > 0
